import React, { useEffect, useState } from 'react';
import BlueBgTitle from '../BlueBgTitle';
import PageButton from '../PageButton';
import PayListTable from './components/PayList';
import PayDetailTable from './components/PayDetail';
import ReportListTable from './components/ReportList';
import TicketGhListTable from './components/TicketGhList';
import TicketJfListTable from './components/TicketJfList';
import QueryListTable from './components/QueryList';
import './index.scss'

const TableBox = (props) => {
    const { data, bgText, tabletype, defaultPage, buttonBoxShow, buttonBoxText, buttonBoxClick } = props;

    const [totalData, setTotalData] = useState(data);
    const [indexList, setIndexList] = useState([]);  //当前渲染的页面数据
    const [current] = useState(1) //当前页码
    const [pageSize, setPageSize] = useState(defaultPage) //每页显示的条数
    const [goValue] = useState(0) //要去的条数index
    const [totalPage, setTotalPage] = useState(0)  //总页数
    const [curByPage, setCurByPage] = useState(1)

    //设置内容
    const setPage = (num) => {
        setIndexList(totalData.slice(num, num + pageSize))
    }

    useEffect(() => {
        setTotalData(data)
    }, [data])

    //设置下一页
    const pageNext = (num) => {
        setPage(num)
    }

    useEffect(() => {
        if (totalData.length > 0) {
            setTotalPage(Math.ceil(totalData.length / pageSize))
            setIndexList(totalData.slice(goValue, goValue + pageSize))
        }

    }, [totalData, goValue, pageSize])

    const updateCurrentByPage = (value) => {
        setCurByPage(value)
    }

    return (
        <div className={"main-table-wrapper " + tabletype}>
            <div className='circle-box left'></div>
            <div className='circle-box right'></div>
            { bgText && <BlueBgTitle title={bgText} /> }
            {
                tabletype === "paylist" ? 
                <PayListTable 
                    list={indexList}
                    onCheck={props.onCheck}
                    toDetails={props.toDetails}
                />:tabletype === "paydetail" ? 
                <PayDetailTable 
                    list={indexList}
                />:tabletype === "reportlist" ? 
                <ReportListTable 
                    list={indexList}
                    allChecked={props.allChecked}
                    onCheck={props.onCheck}
                    onAllChecked={props.onAllChecked}
                />:tabletype === "ticket-gh-list" ? 
                <TicketGhListTable 
                    list={indexList}
                    allChecked={props.allChecked}
                    onCheck={props.onCheck}
                    onAllChecked={props.onAllChecked}
                />:tabletype === "ticket-jf-list" ? 
                <TicketJfListTable 
                    list={indexList}
                    allChecked={props.allChecked}
                    onCheck={props.onCheck}
                    onAllChecked={props.onAllChecked}
                />:tabletype === "querylist" ? 
                <QueryListTable 
                    list={indexList}
                />:<></>
            }
            {(buttonBoxShow && indexList.length > 0) && <div className="all-pay-wrap">
                <div className="all-btn" onClick={() => {
                    buttonBoxClick(data)
                }}>{buttonBoxText}</div>
            </div>}
            {
                (totalData.length > pageSize) && 
                <div className='page-wrap'>
                    <PageButton
                        totalData={totalData}
                        indexList={indexList}
                        totalPage={totalPage}
                        current={current}
                        goValue={goValue}
                        pageSize={pageSize}
                        pageNext={pageNext}
                        updateCurrentByPage={updateCurrentByPage}
                    />
                </div>
            }
        </div>

    )
}


export default TableBox;