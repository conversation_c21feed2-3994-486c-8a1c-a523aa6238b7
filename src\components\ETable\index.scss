.main-table-wrapper {
    position: relative;
    width: 100%;
    height: 826px;
    background-color: #F0F6FF;
    border-radius: 22px;
    border: 1px solid #99C4FF;
    padding: 84px 30px 0;
    &.ticket-gh-list,&.ticket-jf-list{
        height: 736px;
    }
    &.querylist{
        height: 632px;
    }
    .page-wrap {
        position: absolute;
        top: 50%;
        right: -75px;
        transform: translateY(-50%);
        z-index: 10;
        min-height: 590px;
        display: flex;
        align-items: center;
    }

    // .ant-table {
    //     background: transparent;
    // }

    // .ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
    //     background: transparent;
    // }

    // .ant-table-thead>tr>th {
    //     word-break: break-word;
    //     font-size: 30px;
    //     background: none;
    //     text-align: center;
    //     border-bottom: 1px dashed #84B5FF;
    //     padding: 8px 0px;
    // }

    // .ant-table-tbody>tr.ant-table-row:hover>td {
    //     background: none;
    // }

    // .ant-table-tbody>tr>td {
    //     padding: 8px 0px;
    //     font-size: 28px;
    //     height: 34px;
    //     border: none;
    //     text-align: center;
    // }

    // .ant-pagination {
    //     display: none;
    // }

    .circle-box {
        position: absolute;
        top: 136px;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: #FFFFFF;
        border: 1px solid #99C4FF;
        overflow: hidden;

        &.left {
            left: -14px;
            clip: rect(0 28px 28px 13px); //图形裁剪
        }

        &.right {
            right: -14px;
            clip: rect(0 15px 28px 0); //图形裁剪
        }
    }
    &.querylist{
        padding-top: 30px;
        .circle-box{
            top: 80px;
        }
    }

    .ant-col {
        // border: 1px solid red;  

    }

    .table-title-box {
        width: 100%;
        text-align: center;
        line-height: 44px;
        .title {
            font-size: 30px;
            text-align: center;
            color: #333333;
            vertical-align: middle;
        }

        &.title-left {
            text-align: left;
        }

        &.title-right {
            text-align: right;
        }
    }

    .thead-row-box {
        width: 100%;
        border-bottom: 2px dashed #84B5FF;
        padding-bottom: 20px;
        margin-bottom: 24px;
    }
    .checked-box {
        width: 100%;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;

        .item-checked {
            width: 38px;
            height: 38px;
        }
    }

    .tbody-row-box {
        .checked-box {
            height: 38px;
        }
        .table-title-box {
            line-height: 34px;
            margin-bottom: 15px;

            .title {
                font-size: 28px;
                vertical-align: middle;
            }

        }

        &.scroll-outer {
            width: 774px;
            height: 460px;
            padding-right: 50px;
            margin: 0 auto;
        }

        .scroll-inner {
            width: 600px;
        }

        .opt-btn {
            display: inline-block;
            width: 100%;
            min-width: 100px;
            height: 45px;
            background-color: #1677FF;
            text-align: center;
            line-height: 44px;
            font-size: 25px;
            color: #ffffff;
            border-radius: 50px;
        }

    }

    .all-pay-wrap {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);

        .all-btn {
            width: 280px;
            height: 100px;
            background-color: #1677FF;
            border-radius: 50px;
            margin: 30px auto;
            text-align: center;
            color: #ffffff;
            line-height: 100px;
            font-size: 42px;
            -webkit-animation: free_download 0.6s linear alternate infinite;
            animation: free_download 0.6s linear alternate infinite;
        }
    }

    .summary-list-td {
        text-align: center;
        color: #333333;
        line-height: 44px;
        font-size: 30px;
        margin-top: 40px;

        .money {
            color: #FF6116;
        }
    }


}

@-webkit-keyframes free_download{
    0%{-webkit-transform:scale(0.8);}
    100%{-webkit-transform:scale(1.0);}
}
@keyframes free_download{
    0%{transform:scale(0.8);}
    100%{transform:scale(1.0);}
}