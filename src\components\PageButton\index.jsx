
/*
 * @Description: 翻页组件
 */

import React, { useState } from 'react';
import './index.scss';

const PageButton = (props) => {
    
    const {current, pageSize, totalPage, pageNext, updateCurrentByPage} = props;
    const [num, setNum] = useState(0);
    const [pagenum, setPagenum] = useState(current)

    //下一页
    const setNext = () => {
        if(pagenum < totalPage){
            setNum(num+pageSize);
            setPagenum(pagenum+1);
            pageNext(num+pageSize);
            updateCurrentByPage(pagenum+1);
        }
    }

    //上一页
    const setUp = () => {
        if(pagenum > 1){
            setNum(num-pageSize);
            setPagenum(pagenum-1);
            pageNext(num-pageSize);
            updateCurrentByPage(pagenum-1);
        }
    }

    return (
        <div className="change-page">
            <div onClick={setUp} className="up-btn">
                <div className={`arrow-up ${pagenum === 1 ? 'gray' : 'blue'}`}></div>
            </div>
            <div className="page-size">
                <span>第</span>
                <span>{pagenum}</span>
                <span>页</span>
            </div>
            <div onClick={setNext} className="next-btn">
                <div className={`arrow-down ${pagenum === totalPage ? 'gray' : 'blue'}`}></div>
            </div>
        </div>
    )
}


export default PageButton;
