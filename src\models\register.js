const Model = {
	namespace: 'register',
	state: {
		selectDept: {},
		selectDeptFather: {},
		docList: [],
		selectDoc: {},
		selectOrderNum: {},
		selectDay: {},
		lockData: {},
	},
	effects: {
	},
	reducers: {
		queryDepartment(state, action) {
			return { ...state, deptList: action.payload };
		},
		setSelectDeptFather(state, action) {
			return { ...state, selectDeptFather: action.payload.selectDeptFather };
		},
		setSelectDept(state, action) {
			return { ...state, selectDept: action.payload.selectDept };
		},
		queryDoctor(state, action) {
			return { ...state, docList: action.payload };
		},
		setSelectDoc(state, action) {
			return { ...state, selectDoc: action.payload.selectDoc };
		},
		setSelectOrderNum(state, action) {
			return { ...state, selectOrderNum: action.payload.selectOrderNum };
		},
		setSelectDay(state, action) {
			return { ...state, selectDay: action.payload.selectDay };
		},
		setLockData(state, action) {
			return {
				...state,
				lockData: action.payload
			}
		},
	}
};

export default Model;