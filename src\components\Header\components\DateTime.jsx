import React, { useState, useEffect } from 'react';
import moment from 'moment';
import 'moment/locale/zh-cn';

function getOnDay(date){
    const text = ["日","一","二","三","四","五","六"];
    const str = moment(date).format("d");
    return "星期"+text[str]
}

export default function DateTimeText(props) {
    const [date, setDate] = useState(new Date());

    useEffect(() => {
        const timer = setInterval(() => {
            setDate(new Date())
        }, 1000);
        return () => {
            clearInterval(timer)
        }
    }, [date]);

    return (
        <>
            <p className="time">{moment(date).format('HH:mm')}</p>
            <p className="date">{moment(date).format('YYYY-MM-DD')} {getOnDay(date)}</p>
        </>
    )
}