
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import InfoModal from '../../../components/InfoModal';
import ETable from '../../../components/ETable';
import gotoMenu from '../../../utils/gotoMenu';
import PrintModal from '../../../components/PrintModal';
import SuccessModal from '../../../components/SuccessModal';
import RegSteps from '../../../components/Steps';

const data = [
    {
        key: '1',
        deptName: '内科',
        regDate: "2022-09-27",
        price: "4",
    },
    {
        key: '2',
        deptName: '内科',
        regDate: "2022-09-27",
        price: "4",
    },
    {
        key: '3',
        deptName: '内科',
        regDate: "2022-09-27",
        price: "4",
    },
];

const data2 = [
    {
        key: '1',
        price: '4',
        billDate: "2022-09-27",
        type: "核酸缴费",
    },
    {
        key: '2',
        price: '16',
        billDate: "2022-09-27",
        type: "自助缴费",
    },
];
const TicketList = ({
    user: { currentUser },
    pay,
    history,
    dispatch,
}) => {
    const search = history.location.search;
    const queryType = search?search.split("=")[1]?search.split("=")[1]:"":"";

    const [ticketList, setTicketList] = useState([]);
    const [allChecked, setAllChecked] = useState(true);
    const [printModalVisible, setPrintModalVisible] = useState(false);
    const [successModalVisible, setSuccessModalVisible] = useState(false);

    useEffect(()=>{

        const newarr = queryType === "1" ? data : data2;
        const arr = newarr.map((item)=>{
            return {
                ...item, 
                checked: true
            }
        })
        setTicketList(arr)
    },[queryType])


    // 全选
    const onAllChecked = () => {
        let arr = [...ticketList];
        arr.forEach((item) => {
            item.checked = allChecked ? false : true;
        })
        setAllChecked(!allChecked);
        setTicketList(arr);
    }

    // 单选
    const onCheck = (record) => {
        let arr = [...ticketList];
        arr.forEach((item) => {
            if (record.requestOdd === item.requestOdd) {
                item.checked = !item.checked;
            }
        })
        let result = arr.every((item) => {
            return item.checked === true;
        })

        setTicketList(arr);
        setAllChecked(result);
    }

    // 去缴费
    const toPrint = () => {
        let arr = ticketList.filter(((item) => item.checked))
        if(arr.length===0){
            InfoModal("请选择需要打印的项目")
            return;
        }

        // 打印接口
        
        handleOpenSuccessModal();
    }


    // 打印提示弹窗
    const handleOpenPrintModal = () => {
        setPrintModalVisible(true)
    }

    const handleClosePrintModal = () => {
        setPrintModalVisible(false)
    }

    // 打印成功提示
    const handleOpenSuccessModal = () => {
        setSuccessModalVisible(true)
    }

    const handleCloseSuccessModal = () => {
        setSuccessModalVisible(false)
        gotoMenu(history)
    }

    return (
        <>
            <ModuleHeader history={history} title={"凭条补打"} />
            <RegSteps current={1} type="ticket" />
            <div className="table-center ticket-list-wrapper" style={{marginTop: 22}}>
                <ETable
                    tabletype={queryType==="1"?"ticket-gh-list":"ticket-jf-list"}
                    data={ticketList}
                    defaultPage={7}
                    bgText={"打印单"}
                    buttonBoxText="我要打印"
                    buttonBoxShow={true}
                    allChecked={allChecked}
                    buttonBoxClick={toPrint}
                    onCheck={onCheck}
                    onAllChecked={onAllChecked}
                />
            </div>
            {
                printModalVisible && <PrintModal
                    modalVisible={printModalVisible}
                    onCancel={handleClosePrintModal}
                    title={"正在打印第1份报告单，共3份"}
                    subTitle={"尚有1份未出结果"}
                />
            }
            {
                successModalVisible && <SuccessModal 
                    modalVisible={successModalVisible}
                    onCancel={handleCloseSuccessModal}
                    // modalType={"A4"}
                    title={"打印成功，请取走凭条"}
                />
            }
        </>
    )
}


export default connect(({ user }) => ({
    user
}))(TicketList);
