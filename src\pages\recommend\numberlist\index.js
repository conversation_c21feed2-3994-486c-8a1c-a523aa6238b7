import React from 'react';
import moment from 'moment';
import NumPage from '../../../components/RegNumPage';

const RecommNumberList = (props) => {
    
    const {
        history
    } = props;


    return (
        <NumPage
            history={history}
            PAGE_NAME={"预约挂号"}
            PAGE_STEP={1}
            PAGE_TYPE={"recommend"}
            queryParams={{
                beginTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            }}
            nextRouteName="confirm"
        />
    )
}

export default RecommNumberList;

