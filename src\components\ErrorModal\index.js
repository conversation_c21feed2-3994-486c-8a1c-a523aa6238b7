/*
 * @Description: 操作失败弹窗
 */
import React from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';


const ErrorModal = (msg, closeFun) => {
	const Wrapp = ({
		title
	}) => {
		return (
			<div className='error-info-tip-modal-wrap'>
				<img src={require("../../assets/images/modal/confirm.png")} alt="错误图标" />
				<p className='tip-text'>{title}</p>
			</div>
		)
	
	}
	
	let _modal = null

	_modal = Modal.error({
		width: 846,
		centered: true,
		closable: true,
		maskClosable: true,
		icon: <ModalCloseItem num={window.config.TIP_OVER_TIME} onCancel={()=>{
			_modal && _modal.destroy();
			_modal = null;
			if(closeFun){
				closeFun();
			}
		}}/>,
		onCancel: ()=>{
			_modal && _modal.destroy();
			_modal = null;
			if(closeFun){
				closeFun();
			}
		},
		content: <Wrapp title={msg}/>,
		getContainer: document.getElementById("_module"),

	})

	return _modal
}

export default ErrorModal