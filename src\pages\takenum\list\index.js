import React, { useState } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import EList from '../../../components/EList';
import ConfirmModal from '../../../components/ConfirmModal';
import './index.scss';


const data = [
    {
        "deptName": "神经内科门诊",
        "deptID": "112",
        "doctorName": "陆红",
        "doctorID": "20202",
        "doctorType": "1",
        "registeredAmount": " 16.00",
    },
    {
        "deptName": "耳鼻喉科门诊",
        "deptID": "113",
        "doctorName": "李勇",
        "doctorID": "20204",
        "doctorType": "2",
        "registeredAmount": " 30.00",
    }
]
const TakeNumList = ({
    dispatch,
    history,
}) => {

    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [selectNum, setSelectNum]=useState({})
    
    /**
     * @description: 取号
     * @param {*} item
     * @return {*}
     */
    const onTake = (item)=>{
        dispatch({
            type: "takenum/setSelectNum",
            payload: item
        })
        history.push("/takenum/confirm")
    }

    /**
     * @description: 确认取消预约
     * @param {*} item
     * @return {*}
     */
    const onConfirm = () => { 
        // 请求接口
        console.log(selectNum)
        // 取消成功
        // 刷新列表
        // 关闭弹窗
        handleCloseConfirmModal();
    }

    // 取消预约 弹窗
    const handleOpenConfirmModal = (item) => {
        setConfirmModalVisible(true)
        setSelectNum(item)
    }

    const handleCloseConfirmModal = () => {
        setConfirmModalVisible(false)
    }

    return (
        <div className='module-page-warapper'>
            <ModuleHeader history={history} title={"预约取号"} />
            <div className='take-num-wrapper'>
                <EList
                    data={data}
                    defaultPage={3}
                    itemtype="TAKE_NUM"
                    handleClick={onTake}
                    handleOpenConfirmModal={handleOpenConfirmModal}
                />
            </div>
            {
                confirmModalVisible &&  <ConfirmModal
                    modalVisible={confirmModalVisible}
                    title={"是否确认取消当前预约"}
                    okText={"取消预约"}
                    cancelText={"返回"}
                    onCancel={handleCloseConfirmModal}
                    onConfirm={onConfirm}
                />
            }
        </div>
    )

}

export default connect(({ takenum }) => ({
    takenum
}))(TakeNumList);