/*
 * @Description: 搜索框
 */

import React from 'react';
import { Input } from 'antd';
import './index.scss';

const SearchInput = ({
    placeholder,
    onSearch,
    onCharge,
    value
})=>{
    
    const SearchIcon = () => {
        return <img src={require("../../assets/images/btns/search.png")} alt="搜索图标蓝色"/>
    }

    return (
        <div className="search-input-wrapper">
            <Input
                style={{ width: "100%" }}
                placeholder={placeholder}
                suffix={
                    <div onClick={onSearch}><SearchIcon /></div>
                }
                size="large"
                value={value}
                onChange={onCharge}
                allowClear
            />
        </div>
    )

}

export default SearchInput;