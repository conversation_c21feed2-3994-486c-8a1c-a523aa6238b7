.normal-keyboard-drawer {
    .ant-drawer-mask{
        pointer-events: none;
    }
    &.ant-drawer-bottom{
        .ant-drawer-mask{
            background-color: rgba(0, 0, 0, 0.1) !important;
        }
    }
    .ant-drawer-content{
        background-color: rgba(0, 0, 0, 0.8) !important;
        z-index: 100;
    }
    .ant-drawer-body {
        padding: 0 !important;
        .money-title-box {
            padding: 48px 48px 50px;
            border-radius: 16px 16px 0 0;
            .item-title {
                color: #333333;
                font-size: 40px;
                line-height: 58px;
                margin-bottom: 0;
                font-weight: 500;
                &:first-child {
                    margin-bottom: 46px;
                }
                &.gray{
                    color: #CCCCCC;
                }
            }
        }

        .normal-keyboard-wrapper {
            width: 100%;
            user-select: none;
            border-top: 2px solid #F5F5F5;
            padding-top: 50px;
            padding-right: 5px;

            .key-row1{
                padding-left: 60px;
            }

            .key-row2{
                padding-left: 60px;
            }

            .key-row3{
                padding-left: 62px;
            }

            .ant-col{
                width: 80px;
                height: 80px;
                margin-right: 5px;

                &.assure-col{
                    height: 452px;
                    background-color: #1677FF;
                }

                &.right-none{
                    border: none;
                    .item-btn {
                        background-color: rgba(0, 0, 0, 0.2);
                    }
                }
                &.special-btn{
                    width: 110px;
                    .item-btn{
                        font-size: 40px;
                    }
                }
                &.delete-col,&.assure-col{
                    border-right: none;
                }
                &:nth-child(10),&:nth-child(11),&:nth-child(12){
                    border-bottom: none;
                }
            }

            .item-btn {
                height: 100%;
                width: 100%;
                color: #333333;
                text-align: center;
                font-weight: 500;
                font-size: 50px;
                line-height: 80px;
                background-color: #CCCCCC;
                border-radius: 20px;
                &.hover {
                    background-color: #CCCCCC80;
                }
            }
            .item-delete{
                >img{
                    width: 68px;
                    height: 50px;
                }
            }
            .item-assure{
                color: #ffffff;
            }
        }
    }

}