/*
 * @Description: 次要按钮
 */

import React from 'react';
import { Link } from 'dva/router';
import Config from '../../../config';

const MinorItem = ({
    data,
    index,
    shoNoinline,
    findUrl
}) => {

    return (
        shoNoinline ? 
        <div className="small-button-wrapper" >
            <div className='top'>
                {Config.IMGS_TYPE2[data.type] ?
                    <img className='menu-icon' width={Config.IMGS_TYPE2[data.type].width} height={Config.IMGS_TYPE2[data.type].height}
                        src={require("../../../assets/images/icons/" + Config.IMGS_TYPE2[data.type].url + ".png")} alt="主页图标"
                    /> :
                    <img className='menu-icon' width={56} height={56} src="" alt="主页图标" />
                }
            </div>
            <p className='menu-tips'>{data.name}</p>
        </div> : 
        <Link to={findUrl(data)}>
            <div className="small-button-wrapper" >
                <div className='top'>
                    {Config.IMGS_TYPE2[data.type] ?
                        <img className='menu-icon' width={Config.IMGS_TYPE2[data.type].width} height={Config.IMGS_TYPE2[data.type].height}
                            src={require("../../../assets/images/icons/" + Config.IMGS_TYPE2[data.type].url + ".png")} alt="主页图标"
                        /> :
                        <img className='menu-icon' width={56} height={56} src="" alt="主页图标" />
                    }
                </div>
                <p className='menu-tips'>{data.name}</p>
            </div>
        </Link>
        
    )
}


export default MinorItem;