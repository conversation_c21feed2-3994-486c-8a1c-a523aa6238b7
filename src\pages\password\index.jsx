
import React, {useEffect, useRef, useState} from 'react';
import { connect } from 'dva';
import { Row ,Col, Input} from 'antd';
// import moment from 'moment';
import ModuleHeader from '../../components/ModuleHeader';
import InfoModal from '../../components/InfoModal';
import SearchInputButton from '../../components/SearchInputButton';
import RegSteps from '../../components/Steps';
import { getVoice } from '../../services/state';
import './index.scss';

const btnArray = [
    [ '1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    [ 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    [ 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', '清除'],
    [ 'z', 'x', 'c', 'v', 'b', 'n', 'm', '退格' ]
];


const SearchIcon = () => {
    return <img width="40" height="40" src={require("../../assets/images/btns/search2.png")} alt="搜索图标蓝色"/>
}

const Password = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const [dataKey, setmyKeys] = useState('');
    const refs = useRef([])

    useEffect(()=>{
    },[])

    const toList = (type) => {
        history.push(`/query/list?type=${type}`)
    }

    const keyOpt = (val) => {
         if(val === '退格'){
            const item = [...dataKey];
            item.splice(item.length - 1, 1);
            setmyKeys(item.join(''));
        }else if(val === '清除') {
            setmyKeys('');
        }else{
            const item = [...dataKey, val].join('')
            setmyKeys(item);
        }
    };

    const handleTouchStart = (e, i, key) => {
        refs.current[i].style.backgroundColor = "#CCCCCC80"
        refs.current[i].style.color = "#eee"
    }

    const handleTouchEnd = (e, i, key) => {
        refs.current[i].style.backgroundColor = "#bfe2ff"
        refs.current[i].style.color = "#333"
        // keyOpt(key)
        // e.stopPropagation()
    }

    const handleClick = (key) => {
        keyOpt(key)
    }

    return (
        <div className="module-page-wrapper password">
            <ModuleHeader history={history} title={"管理平台"}/>
            <div className="search-input-button-wrapper">
                <div className='input-wrapper'>
                    <Input
                        style={{ width: "100%" }}
                        placeholder={'请输入管理平台密码'}
                        prefix={ <SearchIcon /> }
                        size="large"
                        value={dataKey}
                    />
                </div>
                <div className='btn' onClick={()=>{
                    if(dataKey == 'jws123'){
                        window.location.href = `${window.config.GLLINK}${window.config.DEVICE_CODE}`
                    }else{
                        InfoModal('密码错误，请重试')
                    }
                }}><span>登录</span></div>
            </div>
            <Row className="normal-keyboard-wrapper" gutter={[0, 20]} >
                {
                    btnArray.map((item, rIndex) => {
                        return (
                            <Col span={24} key={`${rIndex}`}>
                                <Row gutter={[0, 20]} className={`key-row${rIndex+1}`}>
                                    {
                                        item.map((i, cindex)=>{
                                            return <>
                                            {
                                                i === '关闭' || i === '退格' || i === '清除' ?
                                                <Col key={`${rIndex}-${cindex}`} className={"special-btn"} >
                                                    <div ref={el => (refs.current[`${rIndex}-${cindex}`] = el)} className='item-btn' onClick={()=>handleClick(i)} onTouchStart={(e)=> handleTouchStart(e, `${rIndex}-${cindex}`, i)} onTouchEnd={(e)=>handleTouchEnd(e, `${rIndex}-${cindex}`, i)}>{i}</div>
                                                </Col> : 
                                                <Col key={`${rIndex}-${cindex}`} className={i === "" ? "right-none" : ""} >
                                                    <div ref={el => (refs.current[`${rIndex}-${cindex}`] = el)} className='item-btn' onClick={()=>handleClick(i)} onTouchStart={(e)=> handleTouchStart(e, `${rIndex}-${cindex}`, i)} onTouchEnd={(e)=>handleTouchEnd(e, `${rIndex}-${cindex}`, i)}>{i.toUpperCase()}</div>
                                                </Col>
                                            } </>
                                        })
                                    }
                                </Row>
                            </Col>
                        )
                    })
                }
            </Row>
        </div>
    )
}

export default connect(({ user }) => ({
    user,
}))(Password);