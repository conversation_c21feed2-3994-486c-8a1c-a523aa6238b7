
/*
 * @Description: 列表组件 + 分页
 */
import React, { useEffect, useState } from 'react';
import Room from './components/Room';
import Doctor from './components/Doctor';
import TakeNum from './components/TakeNum';
import Question from './components/Question';
import PageButton from '../PageButton';
import './index.scss';

const EList = (props) => {

    const { data, handleClick, itemtype, defaultPage } = props;
    const [totalData, setTotalData] = useState(data);
    const [indexList, setIndexList] = useState([]);  //当前渲染的页面数据
    const [current] = useState(1); //当前页码
    const [pageSize, setPageSize] = useState(defaultPage); //每页显示的条数
    const [goValue, setGoValue] = useState(0); //要去的条数index
    const [totalPage, setTotalPage] = useState(0);  //总页数
    const [curByPage, setCurByPage] = useState(1);

    //设置内容
    const setPage = (num) => {
        setIndexList(totalData.slice(num, num + pageSize))
    }

    //设置下一页
    const pageNext = (num) => {
        setPage(num)
        setGoValue(num)
    }

    useEffect(() => {
        setTotalData(data)
        pageNext(goValue)
    }, [data])

    useEffect(() => {
        if (totalData.length > 0) {
            setTotalPage(Math.ceil(totalData.length / pageSize))
            setIndexList(totalData.slice(goValue, goValue + pageSize))
        }

    }, [totalData, goValue, pageSize])

    const updateCurrentByPage = (value) => {
        setCurByPage(value)
    }

    return (
        <div className={'elist-wrapper ' + itemtype}>
            {
                itemtype === "ROOM" ?
                <Room
                    list={indexList}
                    handleClick={handleClick}
                /> : itemtype === "DOCTOR" ?
                <Doctor
                    list={indexList}
                    selectDept={props.selectDept}
                    handleClick={handleClick}
                /> : itemtype === "TAKE_NUM" ?
                <TakeNum
                    list={indexList}
                    handleClick={handleClick}
                    handleCancel={props.handleOpenConfirmModal}
                /> : itemtype === "QUESTIONNAIRE" ?
                <Question
                    list={indexList}
                    handleClick={handleClick}
                    onCheck={props.onCheck}
                /> : <></>
            }
            { (totalData.length > pageSize) &&
                <div className='page-wrap'>
                    <PageButton
                        totalData={totalData}
                        indexList={indexList}
                        totalPage={totalPage}
                        current={current}
                        goValue={goValue}
                        pageSize={pageSize}
                        pageNext={pageNext}
                        updateCurrentByPage={updateCurrentByPage}
                    />
                </div>
            }
        </div>
    );
}

export default EList;