/*
 * @Description:
    提交成功弹窗
 */
import React from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../../../components/ModalCloseItem';
import './index.scss';

const ThanksModal = ({
    modalVisible,
    onCancel,
}) => {

    return (
        <Modal
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={true}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper thanks-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.TIP_OVER_TIME} />
            <img className="modal-img" src={require("../../../assets/images/modal/smile.png")} alt="微笑图片" />
            <h2 className='modal-text'>感谢您的参与!</h2>
            <p className='modal-content'>根据您的意见，我们将对医院的服务进行<br/>反馈和监督，提高您的就医体验。</p>
        </Modal>
    )

}

export default ThanksModal;