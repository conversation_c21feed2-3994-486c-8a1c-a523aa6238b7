.alert-wrapper{
    // display: flex;
    margin: 30px 0;
    position: relative;
    .ttext{
        font-size: 32px;
        line-height: 46px;
        color: #ffffff;
        font-family: 'Source Han Sans CN';
        letter-spacing: 20px;
        text-align: center;
        // text-shadow: 0 3px 6px #000000;
        margin-bottom: 0;
    }
    .alert-button{
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        .button-wrapper{
            width: 180px;
            height: 60px;
            border: 2px solid #fff;
            border-radius: 30px;
            line-height: 58px;
            font-size: 32px;
            color: #fff;
            text-align: center;
        }
    }
    &::after{
        clear: both;
    }
}
