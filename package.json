{"name": "electron", "version": "0.1.0", "main": "main.js", "author": "jws", "description": "...", "homepage": "./", "private": true, "dependencies": {"@babel/core": "7.9.0", "@svgr/webpack": "4.3.3", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@typescript-eslint/eslint-plugin": "^2.10.0", "@typescript-eslint/parser": "^2.10.0", "antd": "^4.7.2", "axios": "^0.21.0", "babel-eslint": "10.1.0", "babel-jest": "^24.9.0", "babel-loader": "8.1.0", "babel-plugin-named-asset-import": "^0.3.6", "babel-preset-react-app": "^9.1.2", "base-64": "^1.0.0", "camelcase": "^5.3.1", "case-sensitive-paths-webpack-plugin": "2.3.0", "crypto-js": "^4.0.0", "css-loader": "3.4.2", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "dva": "^2.4.1", "eslint": "^6.6.0", "eslint-config-react-app": "^5.2.1", "eslint-loader": "3.0.3", "eslint-plugin-flowtype": "4.6.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "^1.6.1", "file-loader": "4.3.0", "fs-extra": "^8.1.0", "html-webpack-plugin": "4.0.0-beta.11", "identity-obj-proxy": "3.0.0", "jest": "24.9.0", "jest-environment-jsdom-fourteen": "1.0.1", "jest-resolve": "24.9.0", "jest-watch-typeahead": "0.4.2", "js-base64": "^3.6.0", "mini-css-extract-plugin": "0.9.0", "moment": "^2.29.1", "optimize-css-assets-webpack-plugin": "5.0.3", "pnp-webpack-plugin": "1.6.4", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "4.0.1", "qrcode-react": "^0.1.16", "react": "^16.14.0", "react-app-polyfill": "^1.0.6", "react-dev-utils": "^10.2.1", "react-dom": "^16.14.0", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "redux": "^4.0.5", "resolve": "1.15.0", "resolve-url-loader": "3.1.1", "sass": "^1.54.5", "sass-loader": "8.0.2", "semver": "6.3.0", "style-loader": "0.23.1", "terser-webpack-plugin": "2.3.8", "ts-pnp": "1.1.6", "url-loader": "2.3.0", "webpack": "4.42.0", "webpack-dev-server": "3.11.0", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "4.3.1", "worker-loader": "^2.0.0"}, "scripts": {"start": "node scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js", "electron-start": "electron .", "dist": "electron-builder"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.example.app", "productName": "test", "asar": true, "directories": {"buildResources": "build", "output": "dist"}, "mac": {"category": "public.app-category.developer-tools", "target": ["dmg", "zip"], "icon": "build/icon.icns"}, "dmg": {"background": "build/background.tiff or build/background.png", "title": "标题", "icon": "build/icon.icns"}, "win": {"target": ["nsis", "zip"]}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "build/bitbug_favicon.ico", "uninstallerIcon": "build/bitbug_favicon.ico", "installerHeaderIcon": "build/bitbug_favicon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "license": "LICENSE.txt"}}, "devDependencies": {"electron": "^10.1.3", "electron-builder": "^22.9.1"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jest-environment-jsdom-fourteen", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "babel": {"presets": ["react-app"]}}