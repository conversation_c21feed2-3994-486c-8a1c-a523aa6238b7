.identity-wrapper{
    width: 880px;
    margin: 0 auto;
    position: relative;
    // border: 1px solid red;
    // padding-top: 60px;

    .blue-text{
        position: absolute;
        top: -80px;
        left: 50%;
        transform: translate(-50%);
        margin-bottom: 10px;
        color: #1677FF;
        font-size: 42px;
        line-height: 60px;
        letter-spacing: 7px;
        text-align: center;
        // border: 1px solid red;
        font-weight: 600;
    }

    .brush-face-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 180px;
        background: url("../../assets/images/others/identity-brush-face-btn-bg.png") no-repeat center;
        background-size: 100% 100%;
        // box-shadow: 0 10px 30px 0 #A3CBF4;
        box-shadow: 0 15px 50px 0 #A3CBF4;
        border-radius: 30px;
        margin-bottom: 52px;
         >img{
            width: 79px;
            height: 79px;
            margin-right: 25px;
         }
        .text{
            font-size: 60px;
            font-weight: 700;
            color: #FFFFFF;
            letter-spacing: 9px;
        }
    } 

    .more-title-box{
        height: 45px;
        margin-bottom: 52px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        >span{
            font-size: 32px;
            color: #666666;
            line-height: 45px;
            font-weight: 500;
        }
        &::before{
            content: "";
            display: inline-block;
            vertical-align: middle;
            width: 232px;
            height: 1px;
            background: #999999;
            margin-right: 64px;
        }
        &::after{
            content: "";
            display: inline-block;
            vertical-align: middle;
            width: 232px;
            height: 1px;
            background: #999999;
            margin-left: 64px;
        }
    }

    .card-wrapper{
        width: 880px;
        border: 1px solid #99c4ff;
        padding: 85px 0 18px;
        position: relative;
        border-radius: 10px;
        background-color: #FAFCFF;

        .card-bg-title-wrapper{
            width: 300px;
            height: 60px;
            position: absolute;
            top: 0px;
            left: 50%;
            margin-left: -150px;
            background: url('../../assets/images/others/card-title-bg.png') no-repeat;
            background-size: 100% 100%;
            text-align: center;
            line-height: 60px;
            color: #1677FF;
            font-size: 42px;
            font-weight: bold;
        }

        &.social{
            margin-bottom: 20px;
            .card-bg-title-wrapper{
                -webkit-animation: free_download 1s linear alternate infinite;
                animation: free_download 1s linear alternate infinite;
            }
        }
    }

    .identity-item{
        background-color: #D7E7FF;
        text-align: center;
        padding: 10px;
        height: 200px;
        border-radius: 20px;
        .card-img{
            width: 245px;
            height: 150px;
            text-align: center;
        }
        .text{
            text-align: center;
            span{
                font-size: 28px;
                color: #1C1C1C;
            }
        }
    }

    .card-content-wrapper{
        padding: 0px 27px;
        .identity-item{
            background-color: #D7E7FF;
            text-align: center;
            padding: 10px;    
            height: 200px;
            border-radius: 20px;
            .card-img{
                width: 245px;
                height: 150px;
                text-align: center;
            }
            .text{
                text-align: center;
                span{
                    font-size: 25px;
                    color: #1677FF;
                }
            }
        }
    }



    .warm-tips-wrapper{
        text-align: center;
        .warm-title{
            color: #333333;
            font-size: 32px;
            line-height: 46px;
            letter-spacing: 5px;
            font-weight: 600;
        }
    }
}

@-webkit-keyframes free_download{
    0%{ 
        width: 300px;
        height: 60px;
    }
    100%{ 
        width: 340px;
        height: 80px;
    }
}
@keyframes free_download{
    0%{ 
        width: 300px;
        height: 60px;
    }
    100%{ 
        width: 340px;
        height: 80px;
    }
}