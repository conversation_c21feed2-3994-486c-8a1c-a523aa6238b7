.reserve-date-wrapper{
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(auto-fill, 240px);
    grid-gap: 24px;
    margin: 32px auto 0;
    padding: 0;
    list-style: none;
    .date-item{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 240px;
        height: 130px;
        background-color: #E8F5FF;
        border-radius: 16px;
        text-align: center;
        color: #333333;
        transition: 0.2s all;
        font-weight: 600;
        .text1{
            font-size: 42px;
            line-height: 60px;
            margin-bottom: 0;
        }
        .text2{
            font-size: 32px;
            line-height: 47px;
        }
        &.selected{
            transform: scale(0.98);
            background-color: #1677FF;
            color: #ffffff;
        }
        &:hover{
            background-color: #1677FF;
            color: #ffffff;
        }
        // &:active{
        //     transform: scale(0.98);
        //     background-color: #1677FF;
        //     color: #ffffff;
        // }

    }
}