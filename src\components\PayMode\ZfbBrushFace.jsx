// 支付宝刷脸支付按钮
import React from "react";
import './index.scss';

const ZfbBrushFace = ({
    history,
    ...props
}) => {

    return (
        <div className="zfb-brush-face-mode-wrapper">
            <div className="content">
                <p className="money"><span style={{fontSize: 24}}>￥</span><b>2.00元</b></p>
                <div className="button-wrapper">
                    <img src={require("../../assets/images/pay/zfb-icon-sm.png")} alt="" />
                    <span>支付宝刷脸付</span>
                </div>
                <p className="tips">仅在点击后采集和识别您的信息</p>
            </div>
        </div>
    )

}

export default ZfbBrushFace;