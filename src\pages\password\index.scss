
.password{
    .search-input-button-wrapper{
        width: 100%;
        height: 64px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24px;
        gap: 24px;
        margin-top: 80px;
        .input-wrapper{
            display: flex;
            align-items: center;
            width: 630px;
            height: 64px;
            background-color: #F0F6FF;
            border-radius: 10px;
            padding: 0 20px;
        }
        .ant-input-affix-wrapper-lg,.ant-input-lg{
            font-size: 28px;
        }
        .ant-input-affix-wrapper-lg{
            padding: 0 !important;
            
        }
        .ant-input-affix-wrapper{
            background: none;
            border: none;
            :focus{
                background: none;
                border: none;
                outline: none;
                box-shadow: none;
            }
            &.ant-input-affix-wrapper-focused{
                background: none;
                border: none;
                outline: none;
                box-shadow: none;
            }
            .ant-input{
                background: none;
                &::placeholder{
                    color: #BBBBBB;
                }
            }
        }
    
        .btn{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 135px;
            height: 64px;
            border-radius: 50px;
            font-size: 30px;
            color: #FFFFFF;
            line-height: 64px;
            background-color: #1677FF;
        }
    }
    
    .normal-keyboard-wrapper {
        width: 100%;
        user-select: none;
        border-top: 2px solid #F5F5F5;
        padding-top: 50px;
        padding-right: 5px;
        margin-top: 80px;
    
        .key-row1{
            padding-left: 40px;
        }
    
        .key-row2{
            padding-left: 40px;
        }
    
        .key-row3{
            padding-left: 25px;
        }
    
        .key-row4{
            padding-left: 100px;
        }
    
        .ant-col{
            width: 80px;
            height: 80px;
            margin-right: 5px;
    
            &.assure-col{
                height: 452px;
                background-color: #1677FF;
            }
    
            &.right-none{
                border: none;
                .item-btn {
                    background-color: rgba(0, 0, 0, 0.2);
                }
            }
            &.special-btn{
                width: 110px;
                .item-btn{
                    font-size: 40px;
                }
            }
            &.delete-col,&.assure-col{
                border-right: none;
            }
            &:nth-child(10),&:nth-child(11),&:nth-child(12){
                border-bottom: none;
            }
        }
    
        .item-btn {
            height: 100%;
            width: 100%;
            color: #333333;
            text-align: center;
            font-weight: 500;
            font-size: 50px;
            line-height: 80px;
            background-color: #bfe2ff;
            border-radius: 20px;
            &.hover {
                background-color: #CCCCCC80;
            }
        }
        .item-delete{
            >img{
                width: 68px;
                height: 50px;
            }
        }
        .item-assure{
            color: #ffffff;
        }
    }
}
