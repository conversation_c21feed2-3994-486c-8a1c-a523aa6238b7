/*
 * @Author: lhy
 * @Date: 2022-07-20 15:04:29
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2022-08-10 14:46:33
 * @FilePath: \自助机web版\src\components\EditNumberModal\index.js
 * @Description: 
 */

import React, { useEffect, useState } from 'react';
import { Modal, Row, Col, Input } from 'antd';
// import { getVoice } from "../../services/state";
import NumKeyboard from './components/NumKeyboard';
import InfoTipModal from '../InfoModal';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const EditNumberModal = props => {
    const { visible, titleText, onCancel, onConfirm } = props;
    const [dataKey, setmyKeys] = useState([]);

    useEffect(()=>{
        // getVoice({speakContent: titleText})
    },[])

    const getConfirm = () => {
        if(dataKey===''){
            InfoTipModal(titleText)
            return false
        }
        onConfirm(dataKey);
    };
    const getKey = (val) => {
        setmyKeys([...dataKey, val].join(''));
    };
    const getClean = () => {
        setmyKeys([]);
    };
    const getBackspace = () => {
        const item = [...dataKey];
        item.splice(item.length - 1, 1);
        setmyKeys(item.join(''));
    };
    
    return (
        <Modal
            destroyOnClose
            title={null}
            visible={visible}
            onCancel={() => onCancel()}
            footer={null}
            width={600}
            centered={true}
            className="edit-number-modal"
            maskClosable={false}
            closable={false}
        >
            <ModalCloseItem num={window.config.READ_CARD_OVER_TIME} onCancel={onCancel} />
            <div className="title">
                <div>{titleText}</div>
            </div>
            <Row className="info-wrapper">
                <Col className="info-col" span={24}>
                    <div className="info-content">
                        <Input placeholder={titleText} type="password" readOnly value={dataKey} className="number" />
                    </div>
                </Col>
                <Col span={24}>
                    <div className="keyboard-wrap" >
                        <NumKeyboard
                            getKey={getKey}
                            getClean={getClean}
                            getBackspace={getBackspace}
                            getConfirm={getConfirm}
                        />
                    </div>
                </Col>
            </Row>
        </Modal>
    );
};

export default EditNumberModal;
