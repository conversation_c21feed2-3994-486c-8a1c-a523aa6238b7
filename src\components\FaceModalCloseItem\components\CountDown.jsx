
/*
 * @Description: 弹窗倒计时
 */
import React, { useState, useRef, useEffect } from 'react';

const ModalCountDown = ({
	onCancel,
	num,
}) => {
	const [count, setCount] = useState(num)
	const timeOutId = useRef(null)

	useEffect(() => {
		timeOutId.current = setTimeout(() => {
			if (count > 1) {
				setCount((c) => c - 1);
			} else {
				onCancel();
			}
		}, 1000);
		return () => clearTimeout(timeOutId.current);
	}, [count]);


	return (
		<span className="modal-count-down">{count}秒</span>
	)
}

export default ModalCountDown