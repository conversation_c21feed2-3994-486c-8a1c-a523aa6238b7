import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getVoice } from '../../services/state';
import ModuleHeader from '../ModuleHeader';
import RegSteps from '../Steps';
import EList from '../EList';
import './index.scss';


const doctorList = [
    {
        doctorName: '张红',
        jobName: "主任医师",
        scheduling: "全日",
        price: "12",
        limitBespeak: 20,
        doctorType: '专家',
        doctorID: 1,
        schedul: 0,
    },
    {
        doctorName: '冯波',
        jobName: "普通",
        scheduling: "全日",
        price: "12",
        limitBespeak: 20,
        doctorType: '专家',
        doctorID: 2,
        schedul: 1,
    },
    {
        doctorName: '赵大',
        jobName: "副主任医师",
        scheduling: "全日",
        price: "12",
        limitBespeak: 20,
        doctorType: '专家',
        doctorID: 3,
        schedul: 2,
    },
    {
        doctorName: '李四',
        jobName: "副主任医师",
        scheduling: "全日",
        price: "12",
        limitBespeak: 20,
        doctorType: '专家',
        doctorID: 4,
        schedul: 3,
    },
    {
        doctorName: '李鸿',
        jobName: "副主任医师",
        scheduling: "全日",
        price: "12",
        limitBespeak: 20,
        doctorType: '专家',
        doctorID: 5,
        schedul: 4,
    },
]

const CommonDoctor = ({
    data,
    handleClick
}) => {
    return (
        <div className="pt-doctor-item" onClick={() => handleClick({...data})}>
            <div className="info">
                <p className="type">普通医生</p>
                <p className="text">挂号费：<span className='money'>10元</span>&nbsp;&nbsp;|&nbsp;&nbsp;余号：<span className="yuhao">0</span></p>
            </div>
            <div className="reg-button-wrapper" >
                <span>挂号</span>
            </div>
        </div>
    )
}

const DoctorPage = ({
    user: { currentUser },
    register: { selectDept },
    history,
    PAGE_NAME,
    PAGE_STEP,
    PAGE_TYPE,
    nextRouteName,
    queryParams,
    dispatch,
}) => {
    useEffect(()=>{
        // getVoice({speakContent: "请选择医生"})
    },[])

    const goNext = (item) => {
        dispatch({
            type: 'register/setSelectDoc',
            payload: {
                selectDoc: item,
            }
        })
        history.push(`/${PAGE_TYPE}/${nextRouteName}`)  
    }

    return (
        <>
            <ModuleHeader history={history} title={PAGE_NAME} />
            <RegSteps current={PAGE_STEP} type={PAGE_TYPE} /> 
            <div className='doctor-wrapper width768'>
                <CommonDoctor handleClick={goNext} data={{
                    doctorName: "普通医生",
                    doctorID: "99",
                    price: "12",
                    limitBespeak: 20,
                    doctorType: '普通',
                }}/>
                <EList 
                    data={doctorList}
                    selectDept={selectDept}
                    itemtype="DOCTOR"
                    defaultPage={4}
                    handleClick={goNext}
                />
            </div>
        </>
    )
}


export default connect(({ user, register }) => ({
    user,
    register
}))(DoctorPage);
