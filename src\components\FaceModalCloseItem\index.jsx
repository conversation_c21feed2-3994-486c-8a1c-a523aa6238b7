/*
 * @Description: 弹窗组件关闭按钮 + 倒计时按钮
 */
import React from 'react';
import { CloseOutlined } from '@ant-design/icons';
import ModalCountDown from './components/CountDown';
import './index.scss';

const FaceModalCloseItem = ({
    onCancel,
    num
})=>{
    
    return (
        <div className='face-modal-close' onClick={onCancel}>
            <CloseOutlined />
            <ModalCountDown onCancel={onCancel} num={num}/>
        </div>
    )

}

export default FaceModalCloseItem;