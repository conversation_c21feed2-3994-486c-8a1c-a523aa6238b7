import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import ModuleHeader from '../../../components/ModuleHeader';
import ConfirmInfoList from '../../../components/ConfirmInfoList';
import PayMode from '../../../components/PayMode';
import BlueBgTitle from '../../../components/BlueBgTitle';


const TakeNumConfirm = ({
    user: { currentUser },
    takenum: { selectNum },
    dispatch,
    history,
}) => {
    const gutter = window.config.PAY_MODE_TYPE === "faceandsm" ? [24, 0] : [0, 44];

    return (
        <>
            <ModuleHeader history={history} title="预约取号" />
            <div className='confirm-page-wrapper'>
                <Row gutter={gutter}>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <div className={window.config.PAY_MODE_TYPE==="faceandsm" ? "confirm-info-list-wrapper col12" : "confirm-info-list-wrapper col24"}>
                            <div className='circle-box left'></div>
                            <div className='circle-box right'></div>
                            <BlueBgTitle title={"取号单"} />
                            <div className='info-bold-box'>
                                <h2 className='info-bold'>患者姓名：张三</h2>
                                <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
                            </div>
                            <Row gutter={[0, 8]}>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>医保金额</span>
                                        <span className='black-span'>10元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>总金额</span>
                                        <span className='black-span'>12元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号科室</span>
                                        <span className='black-span'>内科</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号类别</span>
                                        <span className='black-span'>普通</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>主治医生</span>
                                        <span className='black-span'>普通医生</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>就诊时间</span>
                                        <span className='black-span'>13:30～13:50</span>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </Col>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <PayMode
                            history={history}
                            currentUser={currentUser}
                            dispatch={dispatch}
                            SUCCESS_MODAL_TYPE={7}
                            SUCCESS_MODAL_TITLE={"取号成功，请取走凭条"}
                            PRINT_ERROR_MODAL_TYPE={7}
                            PAYMENT_TYPE="takenum"
                        />
                    </Col>
                </Row>
            </div>
        </>
    )

}

export default connect(({ user, takenum }) => ({
    user,
    takenum
}))(TakeNumConfirm);