import React, {useState} from 'react';
import "./index.scss";

const IndexBar = (props)=>{
    const graphemeArr = ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
    const [selected, setSelected] = useState(0);
    
    const onSelect = (ind) => {
        setSelected(ind)
    }

    return (
        <ul className='index-bar-list'>
            {graphemeArr.map((item,index)=>{
                return (
                    <li className={index===selected?"item selected":"item"} key={index} onClick={()=>onSelect(index)}>
                        <span>{item}</span>
                    </li>
                )
            })}
        </ul>
    )
}


export default IndexBar;