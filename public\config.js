window.config = {
    PRINT_API_NAME: "http://localhost:8080", // 打印服务地址
    STATE_API_NAME: "http://localhost:8080", // 状态服务地址
    API_NAME: "https://aliapi.hndsrmyy.com/hsss-hospital-api-mib",    // 医疗接口服务
    IS_VERTICAL: 1,             // 横屏0 竖屏1 宽屏2   横屏 1920 * 1080  1280 * 1024  竖屏 1080 * 1920   宽屏 1920 * 1080 
    REQUEST_TIME_OUT: 30000,    // 接口响应超时时间
    OVER_TIME: 300,             // 界面超时时间
    PAY_OVER_TIME: 60,          // 支付超时时间
    READ_CARD_OVER_TIME: 60,    // 读卡超时时间
    COMFIRM_OVER_TIME: 60,      // 操作确认弹窗超时时间
    BRUSH_FACE_OVER_TIME: 60,   // 刷脸倒计时时间
    PRINT_OVER_TIME: 360,        // 打印弹窗超时时间
    TIP_OVER_TIME: 3,           // 提示超时时间
    RESERVE_DAY_COUNT: 7,       // 预约挂号天数
    DEVICE_CODE: "jws001",      // 设备号deviceCode
    SAID: "339",                // 测试的医院SAID
    SPBILL_CREATE_IP: '************',       //支付地址IP
    HOS_NAME: '成都双流卫生院',              //院区名称
    CAMERA_ROTATE_ANGLE: '0deg',            //摄像头旋转角度
    PAY_MODE_TYPE: "faceandsm",              //支付结算界面排版
    SERVICE_PHONE: "4008-678-511",           //服务热线
    TECHNICAL_SUPPORT: "京威盛智能",           //技术支持文案
    CLOSE_PROGRAME_PWD: "123456",
    GLLINK: 'http://*************:8099/hsss-cq/#/center?deviceCode='  // 管理平台链接
}