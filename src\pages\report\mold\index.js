
import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';

const QueryMold = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const toList = (type) => {
        history.push(`/report/list?type=${type}`)
    }

    return (
        <>
            <ModuleHeader history={history} title={"报告打印"}/>
            <RegSteps current={0} type="query" />
            <ul className="mold-wrapper report">
                <li className="item bggreen" onClick={()=>toList("1")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/jybg-icon.png")} alt="检验报告打印"/>
                    </div>
                    <p className="title">检验<br/>报告打印</p>
                </li>
                <li className="item bgblue" onClick={()=>toList("2")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/jcbg-icon.png")} alt="检查报告打印"/>
                    </div>
                    <p className="title">检查<br/>报告打印</p>
                </li>
            </ul>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(QueryMold);
