
import React, { useState } from 'react';
import { Drawer, Row, Col, } from 'antd';
import InfoModal from '../InfoModal';
import './index.scss';


const SexSelectDrawer = ({
    visible,
    title,
    modalType,
    onClose,
    onConfirm,
}) => {

    const [select, setSelect] = useState()

    const onSelct = (value) => {
        setSelect(value) 
        onConfirm(value)
    }

    const onAssure = () => {
        if(!select){
            InfoModal("请选择性别");
            return;
        }
        
    }
    return (
        <Drawer
            title={null}
            placement={"bottom"}
            closable={false}
            maskClosable={true}
            onClose={onClose}
            visible={visible}
            height={688}
            key={"sex-select"}
            className="sex-select-drawer"
            getContainer={document.getElementById("_module")}
        >

            <h3 className="item-title">{title}</h3>
            <ul className="select-wrapper">
                <li className="item bggreen" onClick={()=>onSelct("1")}>
                    <img src={require("../../assets/images/icons/sex-select-boy.png")} alt="" />
                    <p className="title">男</p>
                </li>
                <li className="item bgblue" onClick={()=>onSelct("2")}>
                    <img src={require("../../assets/images/icons/sex-select-girl.png")} alt="" />
                    <p className="title">女</p>
                </li>
            </ul>
            {/* <div className='btn-box' onClick={onAssure}><span>确定</span></div> */}
        </Drawer>
    )

}

export default SexSelectDrawer;