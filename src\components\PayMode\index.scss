.pay-mode-wrapper {
    width: 100%;
    height: 100%;
    .zfb-brush-face-mode-wrapper {
        position: relative;
        width: 340px;
        height: 545px;
        background: url("../../assets/images/others/zfb-brush-face-bg.png") no-repeat center;
        background-size: 100% 100%;
        margin: 20px auto 7px;
        .content{
            position: absolute;
            bottom: 10px;
            left: 0;
            right: 0;
            color: #FFFFFF;
            text-align: center;
            p{
                margin: 0;
                padding: 0;
                &.money{
                    font-size: 35px;
                    line-height: 50px;
                    font-weight: 600;
                    text-shadow: 0 2px 4px 0 rgba(0,0,0,0.42);
                }
                &.tips{
                    font-size: 13px;
                    line-height: 20px;
                }
            }

            .button-wrapper{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 298px;
                height: 55px;
                background: linear-gradient(to right, #1699FF, #1764FF);
                border: 1px solid #1677FF;
                border-radius: 27px;
                margin: 10px auto 15px;
                >img{
                    width: 24px;
                    height: 24px;
                    margin-right: 6px;
                }
                >span{
                    font-size: 20px;
                    line-height: 29px;
                }
            }
        }
    }

    .others-pay-wrapper{
        padding: 0 40px;
        .item-pay{
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            >img{
                margin: 15px auto;
                width: 48px;
                height: 48px;
            }
            >p{
                margin: 0;
                font-size: 22px;
                color: #333333;
                line-height: 32px;
            }
        }
    }

    .zfb-shortcut-pay-btn{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 703px;
        height: 135px;
        background: url("../../assets/images/others/zfb-shortcut-pay-bg.png") no-repeat center;
        background-size: 100% 100%;
        margin: 0 auto;
        box-shadow: 0 2px 4px #BADEFF;
        >img{
            width: 50px;
            height: 50px;
            margin-right: 12px;
        }
        >b{
            color: #FFFFFF;
            font-size: 50px;
        }
    }
    .faceandsm-wrapper{
        width: 100%;
        height: 100%;
        background-color: #F0F6FF;
        border-radius: 22px;
        border: 1px solid #99C4FF;
        .title-box{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 35px;
            >span{
                font-size: 24px;
                color: #666666;
                line-height: 35px;
                font-weight: 500;
            }
            &::before{
                content: "";
                display: inline-block;
                vertical-align: middle;
                width: 85px;
                height: 1px;
                background: #979797;
                margin-right: 25px;
            }
            &::after{
                content: "";
                display: inline-block;
                vertical-align: middle;
                width: 85px;
                height: 1px;
                background: #979797;
                margin-left: 25px;
            }
        }
    }
    .sm-wrapper{
       
        .title-box{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 43px;
            >span{
                font-size: 30px;
                color: #333333;
                line-height: 43px;
                font-weight: 600;
            }
            &::before{
                content: "";
                display: inline-block;
                vertical-align: middle;
                width: 237px;
                height: 1px;
                background: #A6A6A6;
                margin-right: 25px;
            }
            &::after{
                content: "";
                display: inline-block;
                vertical-align: middle;
                width: 237px;
                height: 1px;
                background: #A6A6A6;
                margin-left: 25px;
            }
        }
        .others-pay-wrapper{
            width: 790px;
            margin: 0 auto;
            padding: 0 18px;
            .item-pay{
                >img{
                    margin: 15px auto 5px;
                    width: 80px;
                    height: 80px;
                }
                >p{
                    margin: 0;
                    font-size: 25px;
                    line-height: 36px;
                    color: #333333;
                }
            }
        }
    }

}

