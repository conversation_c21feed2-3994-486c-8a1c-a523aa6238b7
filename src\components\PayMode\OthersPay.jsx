// 其他支付方式按钮
import React, { useState } from "react";
import { Row, Col } from 'antd';
import PayModal from '../PayModal';
import './index.scss';

const OthersPay = ({
    history,
    PAYMENT_TYPE,
    ...props
}) => {
    const [payModalVisible, setPayModalVisible] = useState(false);
    const [modalType, setModalType] = useState();
    const [payType, setPayType] = useState();
    


    // 支付二维码弹窗
    const handleOpenPayModal = (modalType) => {
        setPayModalVisible(true)
        setModalType(modalType)
        setTimeout(() => {
            handleClosePayModal();
            // if(PAYMENT_TYPE==="takenum" || PAYMENT_TYPE==="pay"){
            //     props.handleOpenConfirmModal();
            // }else{
            //     props.onPrint();
            // }
            props.onPrint();
        }, 3000)
    }

    const handleClosePayModal = () => {
        setPayModalVisible(false)
    }

    return (
        <>
            <div className='others-pay-wrapper'>
                <div className='title-box'>
                    <span>更多支付方式</span>
                </div>
                <Row gutter={[0, 32]}>
                    <Col span={8}>
                        <div className='item-pay' onClick={()=>handleOpenPayModal(2)}>
                            <img alt="支付宝图片" src={require("../../assets/images/pay/zfb-pay.png")} />
                            <p>支付宝扫码</p>
                        </div>
                    </Col>
                    <Col span={8}>
                        <div className='item-pay' onClick={()=>handleOpenPayModal(1)}>
                            <img alt="微信图片" src={require("../../assets/images/pay/wechat-pay.png")} />
                            <p>微信扫码</p>
                        </div>
                    </Col>
                    <Col span={8}>
                        <div className='item-pay' onClick={()=>handleOpenPayModal(0)}>
                            <img alt="聚合支付图片" src={require("../../assets/images/pay/jh-pay.png")} />
                            <p>聚合扫码</p>
                        </div>
                    </Col>
                </Row>
            </div>
            {
                payModalVisible && <PayModal 
                    modalVisible={payModalVisible}
                    onCancel={handleClosePayModal}
                    modalType={modalType}
                />
            }
        </>
    )

}

export default OthersPay;