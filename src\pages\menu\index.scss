.menu-wrapper,
.recommenu-wrapper {
    .primary-menu {
        margin-bottom: 40px;
    }
    .square-button-wrapper{
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;
        height: 454px;
        border-radius: 16px;
        text-align: center;
        &.bg1,
        &.bg2 {
            background-color: #E9FFFD;
            .icon-box-1 {
                background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }

        &.bg3,
        &.bg4 {
            background-color: #E8F5FF;
            .icon-box-1 {
                background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }

        &.bg5,
        &.bg6 {
            background-color: #FFF4F4;
            .icon-box-1 {
                background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }
        .icon-box-1 {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 137px;
            height: 137px;
            margin: 69px 0 18px;
            >img {
                width: 66px;
                height: 80px;
            }
        }

        .menu-text {
            color: #333333;
            font-size: 60px;
            line-height: 74px;
            font-weight: 600;
            margin-bottom: 55px;
        }

        .menu-tips {
            font-size: 28px;
            line-height: 40px;
            color: #666666;
            margin-bottom: 0;
        }
    }

    .oblong-button-wrapper {
        width: 100%;
        height: 215px;
        border-radius: 16px;
        text-align: center;

        &.bg1,
        &.bg2 {
            background-color: #E9FFFD;

            .icon-box-2 {
                background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }

        &.bg3,
        &.bg4 {
            background-color: #E8F5FF;

            .icon-box-2 {
                background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }

        &.bg5,
        &.bg6 {
            background-color: #FFF4F4;

            .icon-box-2 {
                background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }

        .top {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 32px 28px 20px;

            .icon-box-2 {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100px;
                height: 100px;
                margin-right: 32px;

                >img {
                    width: 50px;
                    height: 58px;
                }
            }

            .menu-text {
                color: #333333;
                font-size: 60px;
                line-height: 74px;
                font-weight: 600;
                margin-bottom: 0;
            }
        }

        .menu-tips {
            font-size: 28px;
            color: #666666;
            line-height: 40px;
            margin-bottom: 0;
        }
    }

    .small-button-wrapper {
        width: 165px;
        height: 180px;
        background-color: #F7F7F7;
        border-radius: 16px;
        text-align: center;

        .top {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 110px;
        }

        .menu-tips {
            font-size: 32px;
            line-height: 46px;
            color: #333333;
            margin-bottom: 0;
        }
    }

    .others-menu-wrapper {
        list-style: none;
        width: 100%;
        padding: 0;
        margin: 0;
        display: flex;
        justify-content: space-between;
    }
    .no-online-menu{
        position: relative;
        .no-online-img{
            position: absolute;
            top: -4px;
            right: -4px;
            width: 67px;
            height: 67px;
            background: url("../../assets/images/others/no-online.png") no-repeat center;
            background-size: 100% 100%;
        }
    }

}

.menu-wrapper {
    width: 100%;
    padding: 40px 42px 0;
}

.recommenu-wrapper {
    // 推荐科室
    width: 100%;

    .recommend-wrapper {
        display: flex;
        align-items: center;
        height: 270px;
        background: linear-gradient(to bottom, #BFE3FF, #F0F9FF);
        border-radius: 32px 32px 0 0;
        padding: 0 42px 0;

        .left-tips {
            color: #1677FF;
            font-size: 48px;
            text-shadow: 5px 5px 0 #B6D5FF;
            margin-right: 40px;
            font-weight: 700;
        }

        .item {
            display: flex;
            align-items: center;
            width: 372px;
            height: 215px;
            background-color: #FFFFFF;
            border-radius: 16px;
            padding: 24px;

            img {
                width: 120px;
                height: 165px;
                margin-right: 25px;
            }

            .info-wrapper {
                p {
                    margin-bottom: 0;

                    &.department {
                        color: #333333;
                        font-size: 28px;
                        // line-height: 78px;
                    }

                    &.doctor {
                        color: #666666;
                        font-size: 22px;
                        margin-bottom: 36px;
                    }
                }

                .reg-btn {
                    width: 132px;
                    height: 50px;
                    background-color: #1677FF;
                    border-radius: 28px;
                    color: #ffffff;
                    font-size: 26px;
                    line-height: 50px;
                    text-align: center;
                }

            }
        }
    }

    .others-menu-wrapper {
        padding: 0 42px;
        margin-top: 39px;
    }

    .right-menu{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 454px;
        .item{
            width: 100%;
        
        }
    }

}