.elist-wrapper{
    position: relative;
    width: 100%;
    margin-top: 16px;
    .page-wrap{
        // position: absolute;
        // top: 50%;
        // right: -75px;
        // transform: translateY(-50%);
        // z-index: 10;
        // min-height: 590px;
        position: absolute;
        top: 0;
        right: -75px;
        z-index: 10;
        // min-height: 590px;
        display: flex;
        align-items: center;
        margin: 22px 0;
    }
    &.QUESTIONNAIRE{
        height: 580px;
        // overflow-y: auto;
        .page-wrap{
            right: -115px;
        }
    }
}
// 科室
.room-list-wrapper{
    list-style: none;
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(auto-fill, 240px);
    grid-template-rows: repeat(4, 130px);
    grid-gap: 24px;
    width: 100%;
    height: 592px;
    padding: 0;
    margin: 0;
    .room-item{
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        width: 240px;
        height: 130px;
        background-color: #E8F5FF;
        border-radius: 16px;
        text-align: center;
        font-size: 32px;
        line-height: 45px;
        color: #333333;
        cursor: pointer;
        &:hover{
            background-color: #1677FF;
            color: #ffffff;
        }
        &:active{
            background-color: #1677FF;
            color: #ffffff;
        }
    }

}
// 医生列表
.doctor-list-wrapper{
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(auto-fill, 374px);
    grid-gap: 10px 20px;
    width: 100%;
    height: 592px;
    padding: 0;
    margin: 0;
    list-style: none;
    .doctor-item{
        width: 374px;
        height: 290px;
        background-color: #FFFFFF;
        border-radius: 14px;
        box-shadow: 0 0 25px rgba(149,155, 169,0.4);
        cursor: pointer;
        .info{
            display: flex;
            // justify-content: space-between;
            margin-bottom: 25px;
            padding: 18px 10px 0 30px;
            >img{
                width: 114px;
                height: 160px;
                margin-right: 13px;
            }
            .right{

            }
            p{
                color: #666666;
                font-size: 20px;
                line-height: 34px;
                margin-bottom: 0;
                letter-spacing: -2px;
                &.name{
                    font-size: 25px;
                    color: #333333;
                }
                .money{
                    color: #FF6116;
                }
            }
        }
        .reg-button-wrapper{
            width: 142px;
            height: 60px;
            border-radius: 30px;
            background-color: #1677FF;
            line-height: 58px;
            font-size: 28px;
            color: #ffffff;
            text-align: center;
            margin: 0 auto;
            
        }
        &.no-schedul{
            cursor: default;
            .reg-button-wrapper{
                background-color: #B3B3B3;
                color: #FFFFFF;
            }
        }
    }
}
// 取号
.take-num-list-wrapper{
    width: 100%;
    height: 852px;
    .item-take-num{
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        height: 268px;
        margin-bottom: 24px;
        box-shadow: 0 0 30px rgba(149, 155, 169, 0.4);
        padding: 28px 32px;
        border-radius: 16px;
        .doctor-pic-box{
            width: 148px;
            height: 210px;
            margin-right: 24px;
            >img{
                width: 100%;
                height: 100%;
            }
        }
        .doctor-info{
            width: 315px;
            .name-type-box{
                margin-bottom: 6px;
                .name{
                    display: inline-block;
                    font-size: 42px;
                    line-height: 60px;
                    color: #333333;
                    margin-right: 10px;
                }
                .type{
                    display: inline-block;
                    height: 38px;
                    padding: 0 18px;
                    border: 2px solid #1677FF;
                    border-radius: 12px;
                    line-height: 34px;
                    font-size: 24px;
                    color: #1677FF;
                }
            }
            p{
                width: 100%;
                height: 52px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin: 0;
                padding: 0;
                line-height: 52px;
                font-size: 28px;
                color: #666666;
                font-weight: 600;
                .black-color{
                    color: #333333;
                }
            }
        }
        .btn-wrapper{
            position: absolute;
            right: 32px;
            .item-btn{
                width: 210px;
                height: 68px;
                text-align: center;
                line-height: 68px;
                font-size: 32px;
                border-radius: 34px;
                &.take-btn{
                    background-color: #1677FF;
                    color: #ffffff;
                    margin-bottom: 24px;
                }
                &.cancel-btn{
                    border: 1px solid #1677FF;
                    color: #1677FF;
                }
            }
        }
    }
}

.question-list-wrapper{
    padding: 36px 26px 0;
    .item-question{
        .question{
            font-size: 30px;
            line-height: 44px;
            color: #333333;
        }
        .options-box{
            padding-left: 88px;
            margin: 28px 0;
            .item-option{
                display: flex;
                align-items: center;
                >img{
                    width: 28px;
                    height: 28px;
                    margin-right: 15px;
                }
                span{
                    font-size: 28px;
                    color: #333333;
                    font-weight: 600;
                }
            }
        }
        &:nth-child(3n){
            .item-question{
                .options-box{
                    margin-bottom: 0;
                }
            }
        }
    }
    
}

