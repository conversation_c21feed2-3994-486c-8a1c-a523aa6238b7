
import React, { useState, useLayoutEffect } from 'react';
import { Input } from "antd";
import { connect } from 'dva';
import { CaretDownOutlined } from '@ant-design/icons';
import InfoModal from '../../../components/InfoModal';
import SuccessTipModal from '../../../components/SuccessTipModal';
import ErrorModal from '../../../components/ErrorModal';
import ModuleHeader from '../../../components/ModuleHeader';
import PhoneKeyboardDrawer from '../../../components/PhoneKeyboardDrawer';
import IDCardKeyboardDrawer from '../../../components/IDCardKeyboardDrawer';
import SexSelectDrawer from '../../../components/SexSelectDrawer';
import EnfantBirthPickerDrawer from '../../../components/EnfantBirthPickerDrawer';
import gotoMenu from '../../../utils/gotoMenu';
import './index.scss';

const PatientAdult = ({
    user: { currentUser },
    history,
}) => {
    const [phone, setPhone] = useState("");  //监护人手机号
    const [enfantName, setEnfantName] = useState("");  //儿童姓名
    const [enfantBirth, setEnfantBirth] = useState("");  //儿童出生年月
    const [enfantSex, setEnfantSex] = useState("");   //儿童性别
    const [enfantIdCard, setEnfantIdCard] = useState("");  //儿童身份证号
    const [phonePickerVisible, setPhonePickerVisible] = useState(false);  //输入手机号键盘
    const [IDCardPickerVisible, setIDCardPickerVisible] = useState(false);  //输入身份证号
    const [sexPickerVisible, setSexPickerVisible] = useState(false);  //选择性别
    const [birthPickerVisible, setBirthPickerVisible] = useState(false);  //选择出生年月

    const toMenu = () => {
        gotoMenu(history)
    }

    // 手输手机号码-抽屉
    const handleOpenPhonePickerDrawer = () => {
        setPhonePickerVisible(true);
    }

    const handleClosePhonePickerDrawer = () => {
        setPhonePickerVisible(false)
    }

    // 手输身份证号-抽屉
    const handleOpenIDcardPickerDrawer = () => {
        setIDCardPickerVisible(true);
    }

    const handleCloseIDCardPickerDrawer = () => {
        setIDCardPickerVisible(false)
    }

    // 选择性别-抽屉
    const handleOpenSexPickerDrawer = () => {
        setSexPickerVisible(true);
    }

    const handleCloseSexPickerDrawer = () => {
        setSexPickerVisible(false)
    }

    // 选择生日-抽屉
    const handleOpenBirthPickerDrawer = () => {
        setBirthPickerVisible(true);
    }

    const handleCloseBirthPickerDrawer = () => {
        setBirthPickerVisible(false)
    }

    /**
     * @description: 确认输入手机号码
     * @param {*} value
     * @return {*}
     */
    const onConfirmPhone = (value) => {
        // 成功 关闭 close
        handleClosePhonePickerDrawer();
        setPhone(value)
    }

    /**
     * @description: 确认输身份证号码
     * @param {*} value
     * @return {*}
     */
    const onConfirmIDCard = (value) => {
        // 成功 关闭 close
        handleCloseIDCardPickerDrawer();
        setEnfantIdCard(value)
    }

    /**
     * @description: 确认输身份证号码
     * @param {*} value
     * @return {*}
     */
    const onConfirmSex = (value) => {
        // 成功 关闭 close
        handleCloseSexPickerDrawer();
        setEnfantSex(value)
    }

    const onConfirmBirth = (value) => {
        // 成功 关闭 close
        handleCloseBirthPickerDrawer();
        setEnfantBirth(value);
    }

    /**
     * @description: 完成按钮
     * @return {*}
     */
    const onSubmit = () => {
        // if (!phone) {
        //     InfoModal("请先输入手机号")
        //     return;
        // }

        // 成功
        SuccessTipModal("建档成功",toMenu);
        // 失败
        // ErrorModal("建档失败，请联系医院工作人员！", toMenu);
    }

    const handleNameChange = (e) => {
        setEnfantName(e.target.value)
    }

    return (
        <div>
            <ModuleHeader history={history} title={"自助建档"} />
            <div className='patient-enfant-wapper'>
                <div className='item'>
                    <span className='left'>监护人姓名：</span>
                    <span className='right'>张三</span>
                </div>
                <div className='item'>
                    <span className='left'>监护人身份证：</span>
                    <span className='right'>330124199610235518</span>
                </div>
                <div className='item'>
                    <span className='left'>家庭住址：</span>
                    <span className='right'>杭州市余杭区华一路*号</span>
                </div>
                <div className='item input-box' onClick={handleOpenPhonePickerDrawer}>
                    <span className='left'>监护人手机号：</span>
                    <Input type="text" readOnly placeholder="点击输入手机号" value={phone} />
                </div>
                <div className='item input-box'>
                    <span className='left'>儿童姓名：</span>
                    <Input type="text" placeholder="点击输入姓名" value={enfantName} onChange={handleNameChange}/>
                </div>
                <div className='item input-box' onClick={handleOpenBirthPickerDrawer}>
                    <span className='left'>儿童出生年月：</span>
                    <Input type="text" readOnly placeholder="点击选择出生年月" value={enfantBirth}  />
                </div>
                <div className='item input-box' onClick={handleOpenSexPickerDrawer}>
                    <span className='left'>儿童性别：</span>
                    <Input type="text" readOnly placeholder="点击选择性别" value={enfantSex==="1"?"男":enfantSex==="2"?"女":""} suffix={<CaretDownOutlined style={{color: "#0060FC",fontSize: "42px"}}/>} />
                </div>
                <div className='item input-box' onClick={handleOpenIDcardPickerDrawer}>
                    <span className='left'>儿童身份证号：</span>
                    <Input type="text" readOnly placeholder="点击输入身份证号" value={enfantIdCard} />
                </div>
                <div className='btn' onClick={onSubmit}><span>完成</span></div>
            </div>
            {phonePickerVisible && <PhoneKeyboardDrawer
                visible={phonePickerVisible}
                title={"手机号"}
                onClose={handleClosePhonePickerDrawer}
                onConfirm={onConfirmPhone}
            />}
            {IDCardPickerVisible && <IDCardKeyboardDrawer
                visible={IDCardPickerVisible}
                title={"儿童身份证号"}
                onClose={handleCloseIDCardPickerDrawer}
                onConfirm={onConfirmIDCard}
            />}
            {sexPickerVisible && <SexSelectDrawer
                visible={sexPickerVisible}
                title={"儿童性别"}
                onClose={handleCloseSexPickerDrawer}
                onConfirm={onConfirmSex}
            />}
            {birthPickerVisible && <EnfantBirthPickerDrawer
                visible={birthPickerVisible}
                title={"儿童出生年月"}
                onClose={handleCloseBirthPickerDrawer}
                onConfirm={onConfirmBirth}
            />}
        </div>
    )
}

export default connect(({ user }) => ({
    user,
}))(PatientAdult);
