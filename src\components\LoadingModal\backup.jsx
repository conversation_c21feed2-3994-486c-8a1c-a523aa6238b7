
import React, { useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const LoadingModal = ({
    modalVisible,
    modalType,
    onCancel,
    modalText
}) => {

    return (
        <Modal
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={false}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper loading-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.TIP_OVER_TIME}/>
            <div className='modal-wrapper'>
                <img className="modal-img" src={require("../../assets/images/modal/loading.png")} alt="加载" />
                <h2 className='modal-text'>{modalText}</h2>
            </div>
        </Modal>
    )

}

export default LoadingModal;