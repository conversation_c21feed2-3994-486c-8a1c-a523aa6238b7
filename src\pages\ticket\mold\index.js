
import React from 'react';
import { connect } from 'dva';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';

const TicketMold = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const toList = (type) => {
        history.push(`/ticket/list?type=${type}`)
    }

    return (
        <>
            <ModuleHeader history={history} title={"凭条补打"}/>
            <RegSteps current={0} type="ticket" />
            <ul className="mold-wrapper ticket">
                <li className="item bggreen" onClick={()=>toList("1")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/ticket-gh-icon.png")} alt="挂号凭条图标"/>
                    </div>
                    <p className="title">挂号凭条</p>
                </li>
                <li className="item bgblue" onClick={()=>toList("2")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/ticket-jf-icon.png")} alt="缴费凭条图标"/>
                    </div>
                    <p className="title">缴费凭条</p>
                </li>
               
            </ul>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(TicketMold);
