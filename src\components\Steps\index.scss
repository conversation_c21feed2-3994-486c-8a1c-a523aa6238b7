.reg-steps{
    text-align: center;
    padding: 0 180px;
    &.steps2{
        padding: 0 290px;  //2个
    }
    &.steps3{
        padding: 0 180px;  //3个
    }
    &.steps4{
        padding: 0 130px;  //4个
    }
    .steps-box{
        .ant-steps-item{
            .ant-steps-item-title{
                font-size: 24px;
                
            }
            .ant-steps-item-tail{
                top: 25px;
                margin-left: 76px;
                padding-left: 45px;
                padding-right: 45px;
                &::after{
                    height: 6px;
                }
            }
            
            .ant-steps-item-icon{
                position: relative;
                width: 60px;
                height: 60px;
                line-height: 38px;
                font-size: 28px;
                border: 10px solid #ffffff;
               
                .ant-steps-icon{
                    color: #ffffff
                }
            }
    
            .ant-steps-item-content{
                width: 140px;
                margin-top: 0;
            }
        }
        .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title{
            color: #1677FF;
        }
       
        .ant-steps-item-container > .ant-steps-item-tail::after {
            background-color: #D8D8D8;
        }
        .ant-steps-item-wait .ant-steps-item-icon{
            background-color: #D8D8D8;
            // border: none;
        }
        .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after{
            background-color: #1677FF;
        }
        .ant-steps-item.ant-steps-item-finish .ant-steps-item-icon{
            background-color: #1677FF;
            // border: none;
        }
        .ant-steps-item.ant-steps-item-process .ant-steps-item-icon{
            background-color: #1677FF;
            // border: none;
        }
        .ant-steps-item.ant-steps-item-process .ant-steps-item-icon{
            border: 10px solid #D0E4FF;
        }
    }
}