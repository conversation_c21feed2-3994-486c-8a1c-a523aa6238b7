import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import ModuleHeader from '../../../components/ModuleHeader';
import ConfirmInfoList from '../../../components/ConfirmInfoList';
import BlueBgTitle from '../../../components/BlueBgTitle';
import PayMode from '../../../components/PayMode';
import RegSteps from '../../../components/Steps';
import "./index.scss";


const NucleinConfirm = ({
    user: { currentUser },
    pay: { selectBill },
    history,
    dispatch,
}) => {
    const gutter = window.config.PAY_MODE_TYPE === "faceandsm" ? [24, 0] : [0, 44];

    return (
        <>
            <ModuleHeader history={history} title={"自助开单"} />
            <RegSteps current={1} type={"nuclein"} /> 
            <div className='confirm-page-wrapper nuclein'>
                <Row gutter={gutter}>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <div className={window.config.PAY_MODE_TYPE==="faceandsm" ? "confirm-info-list-wrapper col12" : "confirm-info-list-wrapper col24"}>
                            <div className='circle-box left'></div>
                            <div className='circle-box right'></div>
                            <BlueBgTitle title={"缴费单"} />
                            <div className='info-bold-box'>
                                <h2 className='info-bold'>患者姓名：张三</h2>
                                <h2 className='info-bold'>自费金额：<span className='red-text'>60元</span></h2>
                            </div>
                            <Row gutter={[0, 8]}>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>医保金额</span>
                                        <span className='black-span'>0元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>总金额</span>
                                        <span className='black-span'>60元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>项目名称</span>
                                        <span className='black-span'>(自费)血常规</span>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </Col>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <PayMode 
                            history={history} 
                            currentUser={currentUser}
                            dispatch={dispatch}
                            SUCCESS_MODAL_TYPE={5}
                            PRINT_ERROR_MODAL_TYPE={2}
                            PAYMENT_TYPE="nuclein"
                            SUCCESS_MODAL_TITLE={"开单成功，请取走凭条"}
                        />
                    </Col>
                </Row>
            </div>
        </>
    )

}

export default connect(({ user, pay }) => ({
    user,
    pay
}))(NucleinConfirm);