
import React, { useState, useEffect } from 'react';
import { Drawer, Row, Col, } from 'antd';
import moment from 'moment';
import InfoModal from '../InfoModal';
import './index.scss';

// 判断某年某月的天数
 
function judgeDayNumber(year, month) {
 
    var days = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
     
    // 判断平年闰年
     
    isLeapYear(year) ? days[1] = 29 : days[1] = 28;
     
    return days[month - 1];
     
}


// 判断是否为闰年,true 闰年，false不是闰年
function isLeapYear(year) {
 
    if (((year % 4) == 0) && ((year % 100) != 0) || ((year % 400) == 0)) {
     
    return true;
     
    } else {
     
    return false;
     
    }
}

const _years = [];
for(let i = 0; i < 21; i++){
    _years.unshift(moment().subtract(i,'years').format('YYYY'))
}

const _months = ["1","2","3","4","5","6","7","8","9","10","11","12"];

const EnfantBirthPickerDrawer = ({
    visible,
    title,
    modalType,
    onClose,
    onConfirm,
}) => {
   
    const defaultYear = moment().format("YYYY");
    const defaultMonth = moment().format("M");
    const defaultDay = moment().format("D");

    const [days, setDays] = useState([]);

    const [selectYear, setSelectYear] = useState(defaultYear);
    const [selectMonth, setSelectMonth] = useState(defaultMonth);
    const [selectDay, setSelectDay] = useState(defaultDay);

    const getDays = (num) => {
        const arr = [];
        for(let i = 1; i<=num; i++){
            arr.push(i.toString())
        }
        setDays(arr);
    }

    useEffect(()=>{
        const num = judgeDayNumber(selectYear,selectMonth);
        getDays(num);

    },[selectYear,selectMonth])
    
    const onChooseYear = (item)=> {
        setSelectYear(item)
    }

    const onChooseMonth = (item)=>{
        setSelectMonth(item)
    }

    const onChooseDay = (item)=>{
        setSelectDay(item)
    }

    const onAssure = () => {
        const time = selectYear+"-"+selectMonth+"-"+selectDay;
        const _time = time ? moment(time).format("YYYY-MM-DD") : "";
        console.log(_time);

        onConfirm(_time);
    }

    return (
        <Drawer
            title={null}
            placement={"bottom"}
            closable={false}
            maskClosable={true}
            onClose={onClose}
            visible={visible}
            key={"enfant-birth-picker"}
            height={868}
            className="enfant-birth-picker-drawer"
            getContainer={document.getElementById("_module")}
        >

            <h3 className="item-title">{title}</h3>
            <Row gutter={[0,0]}>
                <Col span={8}>
                    <div className='item-date-wrapper'>
                        <h3 className='date-type'>年</h3>
                        <Row gutter={[0, 18]}>
                            { _years.map((item, index) => {
                                return <Col span={8} key={index} onClick={()=>onChooseYear(item)}>
                                    <div className={item===selectYear ? 'item-col-wrapper actived' : 'item-col-wrapper'}>
                                        <b className='years-text'>{item}</b>
                                    </div>
                                </Col>
                            })}
                        </Row>
                    </div>
                </Col>
                <Col span={8}>
                    <div className='item-date-wrapper'>
                        <h3 className='date-type'>月</h3>
                        <Row gutter={[0, 76]}>
                            { _months.map((item, index) => {
                                return <Col span={8} key={index} onClick={()=>onChooseMonth(item)}>
                                    <div className={item===selectMonth ? 'item-col-wrapper actived' : 'item-col-wrapper'}>
                                        <b className='years-text'>{item}</b>
                                    </div>
                                </Col>
                            })}
                        </Row>
                    </div>
                </Col>
                <Col span={8}>
                    <div className='item-date-wrapper days'>
                        <h3 className='date-type'>日</h3>
                        <ul className='days-wrapper'>
                            { days.map((item, index) => {
                                return <li className={item===selectDay ? 'item-days actived' : 'item-days'} key={index} onClick={()=>onChooseDay(item)}>
                                    <b className='years-text'>{item}</b>
                                </li>
                            })}
                        </ul>
                    </div>
                </Col>
            </Row>
            <div className='assure-btn-wrapper' onClick={onAssure}><span>确定</span></div>
        </Drawer>
    )

}

export default EnfantBirthPickerDrawer;