
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import { keepTwoDecimalFull } from '../../../utils/utils';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import InfoModal from '../../../components/InfoModal';
import ETable from '../../../components/ETable';



const data = [
    {
        key: '1',
        billingDepartment: '口腔科',
        billingDoctor: '张大三',
        billNo: 23,
        billTime: "2022-09-04",
        billCost: '197.5555',
    },
    {
        key: '2',
        billingDepartment: '中医科',
        billingDoctor: '李云四',
        billNo: 42,
        billTime: "2022-10-18",
        billCost: '166.5',
    },
];

const PayList = ({
    user: { currentUser },
    pay,
    history,
    dispatch,
}) => {

    const [billList, setBillList] = useState([...data]);
    const [allChecked, setAllChecked] = useState(true)

    useEffect(()=>{
        const arr = data.map((item)=>{
            return {
                ...item, 
                checked: true
            }
        })
        setBillList(arr)
    },[])


    // 单选
    const onCheck = (record) => {
        let arr = [...billList];
        arr.forEach((item) => {
            if (record.billNo === item.billNo) {
                item.checked = !item.checked;
            }
        })
        let result = arr.every((item) => {
            return item.checked === true;
        })

        setBillList(arr);
        setAllChecked(result);
    }

    // 计算总金额
    const getTotalCost = (arr) => {
        let money = 0;
        const list = [...arr]
        if (list.length > 0) {
            if (list.length > 1) {
                money = list.reduce((pre, cur) => {
                    return parseFloat(pre) + parseFloat(cur.billCost || 0)
                }, 0)
            } else {
                money = list[0].billCost && parseFloat(list[0].billCost || 0)
            }
        }
        money = keepTwoDecimalFull(money);
        return money;
    }

    // 去缴费
    const toPay = () => {
        let arr = billList.filter(((item) => item.checked))
        if(arr.length===0){
            InfoModal("请选择需要缴费的项目")
            return;
        }
        let billCost = getTotalCost(arr);
        const billNosArr = billList?.map((item)=>item.billNo)??[];
        const bills = billNosArr.join("#");
        dispatch({
            type: 'pay/setSelectBill',
            payload: {
                billCost,
                bills
            },
        })
        history.push("/pay/confirm");
    }

    // 去详情
    const toDetails = (record) => {
        dispatch({
            type: "pay/setSelectBill",
            payload: {
                billCost: record.billCost,
                bills: record.billNo
            }
        })
        history.push("/pay/details")
    }

    return (
        <>
            <ModuleHeader history={history} title={"自助缴费"} />
            <div className="table-center pay-list-wrapper">
                <ETable
                    tabletype="paylist"
                    data={billList}
                    defaultPage={7}
                    bgText={"缴费列表"}
                    buttonBoxText="我要缴费"
                    buttonBoxShow={true}
                    buttonBoxClick={toPay}
                    onCheck={onCheck}
                    toDetails={toDetails}
                />
            </div>
        </>
    )
}


export default connect(({ user, pay }) => ({
    user,
    pay
}))(PayList);
