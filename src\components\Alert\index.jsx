/*
 * @Description: 
 */

import React, { useState, useLayoutEffect } from 'react';
import { Link, routerRedux } from 'dva/router';
import UserInfo from '../UserInfo';
import './index.scss';

let waitTime = 10000;  //该时间间隔内点击才算连续点击（单位：ms)
let lastTime = 0;  //上次点击
let clickCount = 0; //连续点击次数

const Alert = (props)=>{
    
    const hashStr = window.location.hash

    const [headerType, setHeaderType] = useState("menu")
    
    useLayoutEffect(() => {
        if (hashStr.indexOf("menu")>-1) {
            setHeaderType("menu")
        } else if (hashStr.indexOf("identity")>-1) {
            setHeaderType("identity")
        } else if (hashStr.indexOf("password")>-1) {
            setHeaderType("menu")
        } else{
            setHeaderType("others")
        }
    }, [hashStr])

    // 头部多次点击
    const onClickMore = (event) => {

        let currentTime = new Date().getTime();
        console.log(currentTime)
        if (clickCount === 0 || (currentTime - lastTime) >= waitTime) {
            clickCount = 0;
            lastTime = new Date().getTime();
            console.log(lastTime)
        }
        console.log(clickCount)
        clickCount++;
        if (clickCount > 5) {
            clickCount = 0;
            console.log('跳转密码页面')
            props.dispatch(routerRedux.push("/ProductPage"));
        }
    }


    return (
        <div className='alert-wrapper'>
            {
                headerType === "others" ? 
                <UserInfo />:
                <Link to="/password"><p className='ttext'>祝您早日康复！</p></Link>
            }
            {
                headerType !== "menu" && 
                <div className='alert-button'>
                    <Link to="/menu">
                        <div className='button-wrapper'>
                            <span>回到主页</span>
                        </div>
                    </Link>
                    {/* <Link to="/recommenu">
                        <div className='button-wrapper'>
                            <span>智能推荐</span>
                        </div>
                    </Link> */}
                </div>
            }
        </div>
    )

}

export default Alert;