import React from 'react';
import moment from 'moment';
import NumPage from '../../../components/RegNumPage';


const RegNumberList = (props) => {
    
    const {
        history
    } = props;


    return (
        <NumPage
            history={history}
            PAGE_NAME={"当日挂号"}
            PAGE_STEP={2}
            PAGE_TYPE={"register"}
            queryParams={{
                beginTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            }}
            nextRouteName="confirm"
        />
    )
}

export default RegNumberList;

