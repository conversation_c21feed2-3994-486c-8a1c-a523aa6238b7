

const Model = {
	namespace: 'pay',
	state: {
		billList: [],
		selectBill: {},
		selectBillDetail: [],
		payedData: {},
		preSettlementData: {},
		printBillList: [],
		selectN<PERSON>lein: {}, //核酸开单
	},
	effects: {
	},
	reducers: {
		setSelectBill(state, action) {
			return { ...state, selectBill: action.payload };
		},
		setSelectBillDetail(state, action) {
			return { ...state, selectBillDetail: action.payload };
		},
		queryBillDetailList(state, action) {
			return { ...state, billDetailList: action.payload };
		},
		setSelectNuclein(state, action) {
			return { ...state, selectNuclein: action.payload };
		},
	}
};

export default Model;