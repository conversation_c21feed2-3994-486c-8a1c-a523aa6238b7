import React from 'react';

const Room = ({
    list,
    handleClick
})=>{
    return (
        <ul className='room-list-wrapper'>
            {
                list?.map((item,index)=>{
                    return (
                        <li key={index} className="room-item" onClick={() => handleClick(item)}>
                            <span>{item.deptName}</span>
                        </li>
                    )
                })
            }
        </ul>
    )

}

export default Room;