.guide-conclusion {
    padding: 45px 39px;
    .title{
        font-size: 35px;
        color: #333333;
        line-height: 51px;
        margin-bottom: 48px;
    }

    .room-wrapper{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 711px;
        height: 215px;
        background-color: #C4E6FF;
        border-radius: 16px;
        margin: 0 auto 67px;
        padding: 0 24px 0 27px;
        .icon-box{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            background: url("../../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
            background-size: 100% 100%;
        }
        .content{
            display: flex;
            flex-direction: column;
            .content-title{
                font-size: 60px;
                line-height: 74px;
                color: #333333;
                font-weight: 600;
                margin-bottom: 13px;

            }
            .tips{
                font-size: 28px;
                line-height: 41px;
                color: #666666;
                font-weight: 600;
                margin: 0;
            }
        }

        .reg-btns{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 170px;
            height: 65px;
            background: linear-gradient(to bottom, #69CDFF, #4D96FF);
            border-radius: 35px;
            font-size: 35px;
            line-height: 56px;
            color: #ffffff;
        }
    }

    .wram-tips{
        padding-left: 34px;
        >p{
            font-size: 28px;
            color: #666666;
            line-height: 41px;
            margin-bottom: 0;
            font-weight: 600;
        }
    }
}