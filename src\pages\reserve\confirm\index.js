
import React, { useState } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import ModuleHeader from '../../../components/ModuleHeader';
import SuccessModal from '../../../components/SuccessModal';
import SuccessTipModal from '../../../components/SuccessTipModal';
import BlueBgTitle from '../../../components/BlueBgTitle';
import gotoMenu from '../../../utils/gotoMenu';

const ResConfirm = (props) => {

    const {
        user: { currentUser },
        register: { selectDoc, selectDept, selectOrderNum, selectDay},
        history,
    } = props;


    console.log(selectOrderNum)
    const [successModalVisible, setSuccessModalVisible] = useState(false);

    const toMenu = () => {
        gotoMenu(history)
    }

    // 确认预约
    const onResConfirm = () => {
        // handleOpenSuccessModal();
        SuccessTipModal("预约成功",toMenu)
    }

    // // 预约成功提示弹窗
    const handleOpenSuccessModal = (payType) => {
        setSuccessModalVisible(true)
    }

    const handleCloseSuccessModal = () => {
        setSuccessModalVisible(false)
        gotoMenu(history)
    }

    return (
        <>
            <ModuleHeader history={history} title={"信息确认"} />
            <div className='confirm-page-wrapper'>
                <Row gutter={[0, 15]}>
                    <Col span={24}>
                        <div className={"confirm-info-list-wrapper col24"}>
                            <div className='circle-box left'></div>
                            <div className='circle-box right'></div>
                            <BlueBgTitle title={"挂号单"} />
                            <div className='info-bold-box'>
                                <h2 className='info-bold'>患者姓名：张三</h2>
                                <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
                            </div>
                            <Row gutter={[0, 8]}>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>医保金额</span>
                                        <span className='black-span'>10元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>总金额</span>
                                        <span className='black-span'>12元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号科室</span>
                                        <span className='black-span'>{selectDept.deptName}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号类别</span>
                                        <span className='black-span'>{selectDoc.doctorType}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>主治医生</span>
                                        <span className='black-span'>{selectDoc.doctorName}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>就诊日期</span>
                                        <span className='black-span'>{selectDay.date}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>就诊时间</span>
                                        <span className='black-span'>{selectOrderNum.time}</span>
                                    </div>
                                </Col>
                            </Row>
                            <div className='info-button' onClick={() => onResConfirm()}><span>确认</span></div>
                        </div>
                    </Col>
                </Row>
            </div>
            {successModalVisible && <SuccessModal
                    modalVisible={successModalVisible}
                    onCancel={handleCloseSuccessModal}
                    modalType={1}
                    title={"预约成功"}
                />
            }
        </>
    )
}

export default connect(({ user, register }) => ({
    user,
    register
}))(ResConfirm);