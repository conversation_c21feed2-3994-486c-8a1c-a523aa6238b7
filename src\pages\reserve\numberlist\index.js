import React from 'react';
import moment from 'moment';
import NumPage from '../../../components/RegNumPage';


const ResNumberList = (props) => {
    
    const {
        history
    } = props;


    return (
        <NumPage
            history={history}
            PAGE_NAME={"预约挂号"}
            PAGE_STEP={3}
            PAGE_TYPE={"reserve"}
            queryParams={{
                beginTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            }}
            nextRouteName="confirm"
        />
    )
}

export default ResNumberList;

