import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Layout } from 'antd';
import { connect } from 'dva';
import Header from '../../components/Header';
import Ad from '../../components/Ad';
import Alert from '../../components/Alert';
import Footer from '../../components/Footer';
import './index.scss';

const { Content } = Layout;

const HomePage = (props) => {

    const hashStr = window.location.hash

    const [headerType, setHeaderType] = useState("menu")

      // 开启定时器
    const openTimer = () => {
    };
    // 清除定时器
    const clearTimer = () => {
    };
    
    useLayoutEffect(() => {
        if (hashStr.indexOf("menu")>-1) {
            setHeaderType("menu")
        } else if (hashStr.indexOf("identity")>-1) {
            setHeaderType("identity")
        } else {
            setHeaderType("others")
        }
    }, [hashStr])

    // 发送指令给硬件服务
    const sendDeviceOrder = async (data) => {
        // 1重启，2关机，3关闭浏览器
        // const res = await handleDeviceOrder({ ...data });
        // if (!res) return;
    };

    return (
        <Layout className="home-wrapper">
            <Header
                headerType={headerType}
                clearTimer={clearTimer}
                openTimer={openTimer}
                sendDeviceOrder={sendDeviceOrder}
            />
            <Ad/>
            <Alert dispatch={props.dispatch}/>
            <Content>
                <div className={"module-wrapper " + headerType} id="_module">
                    {props.children}
                </div>
            </Content>
            <Footer/>
        </Layout>
    )
}

export default HomePage