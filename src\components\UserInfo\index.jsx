/*
 * @Description: 患者信息
 */
import React from 'react';
import { connect } from 'dva';
import './index.scss';

const UserInfo = ({
    user: { currentUser },
}) => {
    return <ul className='user-info-wrapper'>
        <li className='item'>姓名:张三&nbsp;&nbsp;</li>
        <li className='item'>性别:男&nbsp;&nbsp;</li>
        <li className='item'>病案号:34242519930324</li>
    </ul>
}

export default connect(({ user }) => ({
    user,
}))(UserInfo);
