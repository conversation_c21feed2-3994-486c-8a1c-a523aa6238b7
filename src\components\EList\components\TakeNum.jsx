
import React from 'react';

const TakeNum = ({
    list,
    handleClick,
    handleCancel
}) => {

    return (
        <ul className='take-num-list-wrapper'>
            {
                list?.map((item, index) => {
                    return (
                        <li className='item-take-num' key={index}>
                            <div className='doctor-pic-box'>
                                <img src={require("../../../assets/images/others/doctor.png")} alt="医生照片" />
                            </div>
                            <div className='doctor-info'>
                                <div className='name-type-box'>
                                    <b className='name'>{item.doctorName}</b>
                                    <div className='type'>{item.doctorType === "2" ? "主任医师" : "普通"}</div>
                                </div>
                                <p>挂号科室：<span className='black-color'>{item.deptName}</span></p>
                                <p>预约时间：<span className='black-color'>2022-7-30</span></p>
                                <p>预约时段：<span className='black-color'>下午</span></p>
                            </div>
                            <div className='btn-wrapper'>
                                <div className='item-btn take-btn' onClick={() => handleClick(item)}><span>取号</span></div>
                                <div className='item-btn cancel-btn' onClick={() => handleCancel(item)}><span>取消预约</span></div>
                            </div>
                        </li>
                    )
                })
            }
        </ul>
    )

}

export default TakeNum;