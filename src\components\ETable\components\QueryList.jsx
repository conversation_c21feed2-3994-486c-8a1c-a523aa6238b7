import React from 'react';
import { Row, Col } from 'antd';

const QueryListTable = ({
    list
}) => {
    return (
        <>
            <div className='thead-row-box'>
                <Row>
                    <Col span={16}>
                        <div className='table-title-box title-left'>
                            <b className='title'>项目名称</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>单价</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>单位</b>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className='tbody-row-box'>
                {
                    list.map((item, index) => {
                        return (
                            <Row key={index} >
                                <Col span={16}>
                                    <div className='table-title-box title-left'>
                                        <span className='title'>{item.name}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.price}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.unit}</span>
                                    </div>
                                </Col>
                            </Row>
                        )
                    })
                }
            </div>
        </>
    )

}

export default QueryListTable;