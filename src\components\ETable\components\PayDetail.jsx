import React from 'react';
import { Row, Col } from 'antd';

const PayDetailTable = ({
    list,
}) => {

    return (
        <>
            <div className='thead-row-box'>
                <Row>
                    <Col span={13}>
                        <div className='table-title-box title-left'>
                            <b className='title'>名称</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>单价</b>
                        </div>
                    </Col>
                    <Col span={3}>
                        <div className='table-title-box'>
                            <b className='title'>数量</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>小计</b>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className='tbody-row-box'>
                {
                    list.map((item, index) => {
                        return (
                            <Row key={index} >
                                <Col span={13}>
                                    <div className='table-title-box title-left'>
                                        <span className='title'>{item.name}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.unitPrice}</span>
                                    </div>
                                </Col>
                                <Col span={3}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.amount}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.detailCost}</span>
                                    </div>
                                </Col>
                            </Row>
                        )
                    })
                }
            </div>
        </>
    )

}

export default PayDetailTable;