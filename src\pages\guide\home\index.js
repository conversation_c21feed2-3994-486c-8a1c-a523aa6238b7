
import React, { useState } from 'react';
import { connect } from 'dva';
import ModuleHeader from '../../../components/ModuleHeader';
import BodyParts from './body';
import GuideList from './list';
import RightDrawer from '../RightDrawer';
import './index.scss';

const GuideHome = ({
    user: { currentUser },
    history,
    dispatch,
}) => {
    const [activeKey, setActiveKey] = useState(0)
    const [showDrawer, setShowDrawer] = useState(false)
    const [selectParts, setSelectParts] = useState({})

    /**
     * @description: 切换类型
     * @param {*} index
     * @return {*}
     */
    const onTab = (index)=>{
        setActiveKey(index)
    }

    // 打开症状描述抽屉
    const handleOpenDrawer = () => {
        setShowDrawer(true)
    }
    // 关闭症状描述抽屉
    const handleCloseDrawer = () => {
        setShowDrawer(false)
    }
    
    const toConclusion = (value)=> {
        history.push("/guide/conclusion");
    }

    const onSelectParts = (value) => {
        // 请求接口
        setSelectParts({...value })
        handleOpenDrawer();
    }

    return (
        <>
            <ModuleHeader history={history} title={"智能导诊"}/>
            <div className='guide-bg-wrapper' id="guide-page">
                <ul className='menu-lists'>
                    <li className={activeKey===0?"item-menu actived" : "item-menu"} onClick={()=>onTab(0)} >身体部位</li>
                    <li className={activeKey===1?"item-menu actived" : "item-menu"} onClick={()=>onTab(1)} >列表</li>
                </ul>
                {   activeKey === 0 ?
                    <BodyParts onSelectParts={onSelectParts}/> : 
                    <GuideList onSelectParts={onSelectParts}/>
                }
                {/* 右侧症状描述抽屉 */}
                { showDrawer && 
                    <RightDrawer
                        visible={showDrawer}
                        data={selectParts}
                        onClose={handleCloseDrawer}
                        toConclusion={toConclusion}
                    />
                }
            </div>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(GuideHome);
