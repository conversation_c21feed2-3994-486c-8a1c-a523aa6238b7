/*
 * @Description: 
 */
import React from 'react';
import { Row, Col } from 'antd';

const Question = ({
    list,
    onCheck
}) => {


    const Options = ({ question }) => {
        return (
            <Row gutter={[100, 28]}>
                {
                    question.options.map((option) => {
                        return (
                            <Col span={12} key={option.id}>
                                <div className='item-option' onClick={() => onCheck(question, option)}>
                                    {
                                        option.checked ?
                                            <img src={require("../../../assets/images/btns/checked.png")} alt="" /> :
                                            <img src={require("../../../assets/images/btns/no-check.png")} alt="" />
                                    }
                                    <span>{option.name}</span>
                                </div>
                            </Col>
                        )
                    })
                }
            </Row>
        )
    }

    return (
        <div className='question-list-wrapper'>
            {
                list.map((question) => {
                    return (
                        <div className='item-question' key={question.questionIndex}>
                            <h3 className='question'>{question.questionIndex + "." + question.question}</h3>
                            <div className='options-box'>
                                <Options question={question}/>
                            </div>
                        </div>
                    )
                })
            }
        </div>
    )

}

export default Question;