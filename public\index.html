<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <script type="text/javascript" src="%PUBLIC_URL%/config.js"></script>

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>智慧医疗自助服务系统</title>
  </head>
  <body>

    <script type="text/javascript">
      ! function (e, t, a) {
         function r() {
             for (var e = 0; e < s.length; e++) s[e].alpha <= 0 ? (t.body.removeChild(s[e].el), s.splice(e, 1)) : ( s[e].scale += .002, s[e].alpha -= .013, s[e].el.style.cssText = "left:" + s[e].x +
                 "px; pointer-events: none; top:" + s[e].y + "px; border-radius: 50%; opacity:" + s[e].alpha + ";background:" + s[e].color + ";z-index:99999; transform: scale("+ s[e].scale +")");
             requestAnimationFrame(r)
         }
    
         function n() {
             var t = "function" == typeof e.onclick && e.onclick;
             e.onclick = function (e) {
                t && t(), o(e)
             }
         }
    
         function o(e) {
             var a = t.createElement("div");
             a.className = "heart", s.push({
                 el: a,
                 x: e.clientX - 25,
                 y: e.clientY - 25,
                 scale: 1,
                 alpha: 1,
                 color: c()
             }), t.body.appendChild(a)
         }
    
         function i(e) {
             var a = t.createElement("style");
             a.type = "text/css";
             try {
                 a.appendChild(t.createTextNode(e))
             } catch (t) {
                 a.styleSheet.cssText = e
             }
             t.getElementsByTagName("head")[0].appendChild(a)
         }
    
         function c() {
             return "rgb(" + ~~(255 * Math.random()) + "," + ~~(255 * Math.random()) + "," + ~~(255 * Math
                 .random()) + ")"
         }
         var s = [];
         e.requestAnimationFrame = e.requestAnimationFrame || e.webkitRequestAnimationFrame || e
             .mozRequestAnimationFrame || e.oRequestAnimationFrame || e.msRequestAnimationFrame || function (e) {
                 setTimeout(e, 3)
             }, i(
                 ".heart{width: 50px;height: 50px;position: fixed;background: #f00; border-radius: 50%;}"
             ), n(), r()
     }(window, document);
  
  
    </script>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
