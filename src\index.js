import dva from 'dva';
import './index.scss';
import './assets/scss/base.scss';
import './utils/getDeviceCode';

const createHistory = require('history').createHashHistory;

//1.Initialize
const app = dva({
    history: createHistory(),
});

//2.Plugins
//app.use({});

//3.Model
require('./models').default.forEach(key => app.model(key.default));

//4.Router
app.router(require('./routes/router').default);

//5.Start
app.start('#root');