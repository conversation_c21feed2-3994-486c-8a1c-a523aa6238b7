import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Input } from "antd";
import "./index.scss";

const SmInput = forwardRef((props, ref)=>{
    let code = "";
    let lastTime, nextTime;
    let lastCode, nextCode;

    const inputEl = useRef(null);

    useImperativeHandle(ref, () => inputEl.current);

    useEffect(()=>{
        document.getElementById("sminput").addEventListener('keypress',handleKeyChange)
        return ()=>{
            window.removeEventListener('keypress',handleKeyChange)
            code = "";
        }
    },[])

    const handleKeyChange = (e) => {
        console.log("=====监听键盘事件========")
        if (window.event) { // IE
            nextCode = e.keyCode
        } else if (e.which) { // Netscape/Firefox/Opera
            nextCode = e.which
        }
        if (e.which === 13) {
            if (code.length < 3) return // 手动输入的时间不会让code的长度大于2，所以这里只会对扫码枪有
            // console.log(code)
            // console.log('扫码结束')
            // console.timeEnd()
            parseQRCode(code) // 获取到扫码枪输入的内容，做别的操作
            code = ''
            lastCode = ''
            lastTime = ''
            return
        }
        nextTime = new Date().getTime()
        if (!lastTime && !lastCode) {
            // console.log('扫码开始。。。')
            code += e.key
        }
        if (lastCode && lastTime && nextTime - lastTime > 500) { // 当扫码前有keypress事件时,防止首字缺失
            // console.log('防止首字缺失。。。')
            code = e.key
        } else if (lastCode && lastTime) {
            // console.log('扫码中。。。')
            code += e.key
        }
        lastCode = nextCode
        lastTime = nextTime
    }
    
    const parseQRCode = (code) => {
        console.log('parsecode', code)
        if (code) {
            props.onListenSmCode(code)
        }
    }

    return (
        <div className="sm-input-wrapper" id="sminput" tabIndex="-1" >
            <Input ref={inputEl} type="text" value={props.value} placeholder="扫码或手输门诊号" />
        </div>
    )

})

export default SmInput;