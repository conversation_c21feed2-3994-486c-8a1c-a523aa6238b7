/*
 * @Description: 步骤条
 */
import React from 'react';
import { Steps } from 'antd';
import Config from '../../config';
import './index.scss';

const { Step } = Steps;
const RegSteps = ({
    current,
    type
}) => {
    const data = Config?.menuData;

    let datasteps = [] 
    data.forEach((item)=>{
        if(item.type === type) {
            datasteps = item.steps
        }
    })

    return (
        <div className={"reg-steps steps" + datasteps.length}>
            <Steps direction="horizontal" labelPlacement="vertical" current={current} className="steps-box">
                {
                    Config.menuData.map(item => {
                        if(item.type === type) {
                            return item.steps.map(title => {
                                return <Step title={title}/>
                            })
                        }
                    })
                }
            </Steps>
        </div>
    )
}

export default RegSteps