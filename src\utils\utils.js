import moment from 'moment';

export const getQueryString = (search, name) => {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

// 处理返回的数据是OBJECT 或者是 数组
export const ObjectToArr = (arr)=>{
    let type = arr && Object.prototype.toString.call(arr);//固原--只查询出一个，res.data的数据类型为object;
    let temparr = [];
    if (type === "[object Object]") {
        temparr = [arr];
    } else {
        temparr = arr;
    }
    return temparr;
}

export const timeToSec = (time)=>{
    var hour = time.split(':')[0]
    var min = time.split(':')[1]
    var s = Number(hour * 3600) + Number(min * 60) 
    return s * 1000
}

// 时间段过期号源处理
export const getOverdueNum = (arr) => {
    let newarr = [...arr]
    newarr.forEach((item)=>{
        let beginTime = item.beginTime ? timeToSec(item.beginTime) : 0;
        let endTime = item.endTime ? timeToSec(item.endTime) : 0;
        let curDate = timeToSec(moment().format('HH:mm'));
        if (curDate >= endTime) {
            item.isOverdue = true;
        }
    });
    return newarr;

}

// Js根据身份证号获取出生日期、性别和年龄
export const IdCard = (IdCard, type) => {
    if (type === 1) {
        //获取出生日期
        let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
        return birthday
    }
    if (type === 2) {
        //获取性别
        if (parseInt(IdCard.substr(16, 1)) % 2 == 1) {
            return "1"
        } else {
            return "2"
        }
    }
    if (type === 3) {
        //获取年龄
        let ageDate = new Date()
        let month = ageDate.getMonth() + 1
        let day = ageDate.getDate()
        let age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
        if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) == month && IdCard.substring(12, 14) <= day) {
            age++
        }
        if (age <= 0) {
            age = 1
        }
        return age
    }
}

// 姓名脱敏
export const NamePrivate = (name) => {
    if(!name){ return ""}
    if(name.length===2){
        return name.substring(0,1)+'*' //截取name 字符串截取第⼀个字符，
    }else if(name.length===3){
        return name.substring(0,1)+"*"+name.substring(2,3)//截取第⼀个和第三个字符
    }else if(name.length>3){
        return name.substring(0,1)+"*"+'*'+name.substring(3,name.length)//截取第⼀个和⼤于第4个字符
    }
}

// 身份证号脱敏
export const IdNoPrivate = (idNo) => {
    if(!idNo){ return ""}
    const id = idNo.replace(/(\w{5})\w*(\w{4})/, '$1*********$2');
    return id
}

// 金额计算精度
export function keepTwoDecimalFull(num) {
    let result = parseFloat(num)
    if (isNaN(result)) {
        // alert('传递参数错误，请检查')
        return false
    }
    result = Math.round(num * 100) / 100
    let s_x = result.toString()
    let pos_decimal = s_x.indexOf('.')
    if (pos_decimal < 0) {
        pos_decimal = s_x.length
        s_x += '.'
    }
    while (s_x.length <= pos_decimal + 2) {
        s_x += '0'
    }
    return s_x
}