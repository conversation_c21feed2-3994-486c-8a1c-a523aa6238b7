import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Row, Col } from 'antd';
import OblongItem from './components/OblongItem';
import MinorItem from './components/MinorItem';
import Config from '../../config';
import './index.scss';

const Menu = ({
    dispatch,
    history
}) => {
    
    // const data = Config.menuData;
    
    // const findUrl = (value, key="name") => {
    //     if(!value) return ''
    //     if(value==="更多服务") return "/more";
    //     return data.find(item => item[key] === value).menu_link || '';
    // }

    const toPage = (data) => {
        if(!data.name) return ''
        return data.menu_link;
    }

    return (
        <div className='menu-wrapper'>
            <div className='primary-menu'>
                <Row gutter={[20, 24]}>
                    <Col span={12}>
                        <OblongItem
                            index={1} 
                            data={{
                                type: 'register',
                                menu_link: '/register/identity?nextpage=/register/roomlist',
                                name: '当日挂号',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <OblongItem
                            index={2} 
                            data={{
                                type: 'takenum',
                                menu_link: '/takenum/identity?nextpage=/takenum/list',
                                name: '预约取号',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <OblongItem
                            index={3} 
                            data={{
                                type: 'reserve',
                                menu_link: '/reserve/identity?nextpage=/reserve/date',
                                name: '预约挂号',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <OblongItem
                            index={4} 
                            data={{
                                type: 'pay',
                                menu_link: '/pay/identity?nextpage=/pay/list',
                                name: '自助缴费',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <OblongItem
                            index={5} 
                            data={{
                                type: 'report',
                                menu_link: '/report/identity?nextpage=/report/mold',
                                name: '报告打印',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <OblongItem
                            index={6} 
                            data={{
                                type: 'patient',
                                menu_link: '/patient/identity?nextpage=/patient/create',
                                name: '自助建档',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                </Row>
            </div>
            <ul className='others-menu-wrapper'>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'query',
                            menu_link: '/query/mold',
                            name: '自助查询',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'ticket',
                            menu_link: '/ticket/identity?nextpage=/ticket/mold',
                            name: '凭条补打',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'questionnaire',
                            menu_link: '/questionnaire/identity?nextpage=/questionnaire/home',
                            name: '满意度调查',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'guide',
                            menu_link: '/guide/home',
                            name: '智能导诊',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu no-online-menu'>
                    <MinorItem data={{
                        type: 'nuclein',
                        name: '项目开单',
                        menu_link: '/hskd/identity?nextpage=/hskd/list',
                    }} findUrl={toPage}/>
                </li>
            </ul>
        </div>
    )
    
}

export default Menu;