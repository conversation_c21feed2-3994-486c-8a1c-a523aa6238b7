// request.js
import React from 'react'
import axios from 'axios';
import { Modal } from 'antd';
import ErrorModal from '../components/ErrorModal';
import './getDeviceCode';

/**
 * 一、功能：
 * 1. 统一拦截http错误请求码；
 * 2. 统一拦截业务错误代码；
 * 3. 统一设置请求前缀
 * |-- 每个 http 加前缀 baseURL = /api/v1，从配置文件中获取 apiPrefix
 * 4. 配置异步请求过渡状态：显示蓝色加载条表示正在请求中，避免给用户页面假死的不好体验。
 * |-- 使用 NProgress 工具库。
 * 
 * 二、引包：
 * |-- axios：http 请求工具库
 * |-- NProgress：异步请求过度条，在浏览器主体部分顶部显示蓝色小条
 * |-- notification：Antd组件 > 处理错误响应码提示信息
 * |-- routerRedux：dva/router对象，用于路由跳转，错误响应码跳转相应页面
 * |-- store：dva中对象，使用里面的 dispatch 对象，用于触发路由跳转
 */
// 状态码错误信息
const codeMessage = {
	200: '服务器成功返回请求的数据。',
	201: '新建或修改数据成功。',
	202: '一个请求已经进入后台排队（异步任务）。',
	204: '删除数据成功。',
	400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
	401: '用户没有权限（令牌、用户名、密码错误）。',
	403: '用户得到授权，但是访问是被禁止的。',
	404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
	406: '请求的格式不可得。',
	410: '请求的资源被永久删除，且不会再得到的。',
	422: '当创建一个对象时，发生一个验证错误。',
	500: '服务器发生错误，请检查服务器。',
	502: '网关错误。',
	503: '服务不可用，服务器暂时过载或维护。',
	504: '网关超时。',
};

const { GUIDE_API_NAME, STATE_API_NAME, PRINT_API_NAME, API_NAME, IS_VERTICAL, REQUEST_TIME_OUT } = window.config;

// const instance = axios.create();

// 设置全局参数，如响应超市时间，请求前缀等。
axios.defaults.timeout = REQUEST_TIME_OUT;
// instance.defaults.withCredentials = true;

let reqCount = 0;
let loadingModal = null;

const showLoading = ()=>{
	if(reqCount===0){
		loadingModal = Modal.info({
			width: IS_VERTICAL!==1?720:860,
			icon: false,
			className: "loading-modal",
			centered: true,
			title: <div>
				<div className="loading-text">正在加载中请稍候......</div>
				<img src={require("../assets/images/gifs/loading.gif")} alt="加载动图" className="loading-image" />
			</div>,
			okButtonProps: false,
		});
	}

	reqCount++;
}
const hideLoading = ()=>{
	if(reqCount <= 0) return;
	reqCount--;
	if(reqCount===0){
		loadingModal && loadingModal.destroy();
		loadingModal = null;
	}
}

// 添加一个请求拦截器，用于设置请求过渡状态
axios.interceptors.request.use((config) => {
	// 请求开始，蓝色过渡滚动条开始出现
	// NProgress.start();
	let baseURL = API_NAME
	if (config.isGuide) {
		baseURL = GUIDE_API_NAME
	} else if (config.isState) {
		baseURL = STATE_API_NAME
	} else if (config.isPrint) {
		baseURL = PRINT_API_NAME
	} else {
		baseURL = API_NAME
	} 
	// `params` 是即将与请求一起发送的 URL 参数
	if(config.isState || config.isPrint){
		config.params = {
			...config.params
		}
	}else{
		config.params = {
			...config.params,
			deviceCode: window.config.DEVICE_CODE
		}
	}
	if(!config.isHideLoading){
		showLoading(config.isHideLoading);
	}
	return {
		...config,
		baseURL
	};
}, (error) => {
	hideLoading();
	return Promise.reject(error);
});

// 添加一个返回拦截器
axios.interceptors.response.use((response) => {
	if(!response.config.isHideLoading){
		hideLoading();
	}
	return response;
}, (error) => {
	hideLoading();
	return Promise.reject(error);
});

export default function request(opt) {
	// 调用 axios api，统一拦截
	return axios(opt)
		.then((response) => {
			// console.log(response)
			// >>>>>>>>>>>>>> 请求成功 <<<<<<<<<<<<<<
			// console.log(`【${opt.method} ${opt.url}】请求成功，响应数据：%o`, response);
			// 业务错误提示
			if (response.data && response.data.code !== '0') {
				if(!opt.isHideError){
					ErrorModal(response.data.msg)
				}
				if(opt.isReturnErrorRes){
					return {...response.data}
				}
				return false;
			}
			return { ...response.data };
		}).catch((error) => {
			// >>>>>>>>>>>>>> 请求失败 <<<<<<<<<<<<<<
			// console.log(error)
			// 请求配置发生的错误
			if (!error.response) {
				// console.log('Error', error.message);
				if (!opt.isHideError) {
					ErrorModal(error.message)
				}
				return false
			}
			// 响应时状态码处理 
			const status = error.response.status;
			const errortext = codeMessage[status] || error.response.statusText;
			if(!opt.isHideError){
				ErrorModal(errortext)
			}
			return false
		});
}