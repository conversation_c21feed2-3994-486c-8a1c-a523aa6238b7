
import React, { useState, useEffect } from 'react';
import { RightOutlined } from '@ant-design/icons';
import Data from './data';
import './index.scss';

const BodyParts = ({
    onSelectParts
}) => {
    const KEYS = ["man_coords_list1","man_coords_list2","woman_coords_list1","woman_coords_list2",
    "boy_coords_list1","boy_coords_list2","girl_coords_list1","girl_coords_list2"];

    const [sex, setSex] = useState("man");
    const [type, setType] = useState("adult");
    const [front, setFront] = useState(true);
    const [coordsList, setCoordsList] = useState({});

    useEffect(()=>{
        getData();
    },[])

    const getData = () => {
        let list = Data.coords_list
        for(let i=0; i<KEYS.length; i++){
            if(!list[KEYS[i]]){
                continue;
            }
            let arr = list[KEYS[i]];
            list[KEYS[i]] = adjust(arr,KEYS[i])  
        }
        setCoordsList(list)
    }   

    /**
     * @description: 选择性别
     * @param {*} sex string
     * @return {*}
     */
    const onSex = (sex) => {
        setSex(sex)
    }

    /**
     * @description: 选择身体类型
     * @param {*} type string
     * @return {*}
     */
    const onType = (type) => {
        setType(type)
    }

    /**
     * @description: 正背面
     * @return {*}
     */
    const onFront = () => {
        setFront(!front)
    }

    /**
     * @description:  
     * @param {*} data  图片定位数据list
     * @return {*}
     */
    const adjust = (data) => {
        let currentList = [...data];  
        let newList = currentList.map((el)=>{
            let position = adjustPosition(el.position);
            return {
                ...el,
                position
            }
        })
        return newList;
    }

    /**
     * @description: 计算对应点位的真实坐标值
     * @param {*} coords  原坐标值
     * @return {*}
     */
    const adjustPosition = (coords) => {
        const imgWidth = 316;  //坐标是根据这个尺寸来换算坐标比例
        const imgHeight = 630;  
        const imgDoms = document.getElementsByClassName("body-image")[0];
        const imgTrueHeight = imgDoms.clientHeight;
        const imgTrueWidth = Math.round(imgTrueHeight * (imgWidth / imgHeight));
        // console.log(imgDoms)
        // console.log(imgTrueWidth)
        // console.log(imgTrueHeight)
        const arr = coords?.split(",")??[];
        const newcoordsarr = arr.map((item, ind) => {
            let newItem = {};
            if (ind % 2 === 1) {
                // y轴坐标
                newItem = Math.round(imgTrueHeight * (parseInt(item) / imgHeight)).toString();
            } else {
                // x轴坐标
                newItem = Math.round(imgTrueWidth * (parseInt(item) / imgWidth)).toString();
            }
            return newItem
        })
        const newcoordsstr = newcoordsarr?.join(",")??"";
        return newcoordsstr;
    }

    /**
     * @description: 点击身体部位 查询症状列表
     * @param {*} item
     * @return {*}
     */
    const onBody= (item) => {
        console.log(item)
        const partsType = {
            "header": "头部",
            "face": "耳眼口鼻",
            "neck": "颈部",
            "chest": "胸部",
            "belly": "腹部",
            "genital": "生殖器",
            "arm_left": "手部",
            "arm_right": "手部",
            "leg": "腿部",
            "foot": "脚部",
            "skin": "皮肤",
        }
        onSelectParts({...item, parts: partsType[item.type]});
    }


    // console.log(coordsList)
    return (
        <div className='body-parts-wrapper'>
            {/* 顶部按钮 */}
            <div className='btns-wrapper'>
                <div className='sexs'>
                    <div className={sex === "man" ? "item actived" : "item"} onClick={() => onSex("man")}>男性</div>
                    <div className={sex === "woman" ? "item actived" : "item"} onClick={() => onSex("woman")}>女性</div>
                </div>
                <div className='types'>
                    <div className={type === "adult" ? "item actived" : "item"} onClick={() => onType("adult")}>成人</div>
                    <div className={type === "enfant" ? "item actived" : "item"} onClick={() => onType("enfant")}>儿童</div>
                </div>
            </div>
            {/* map img */}
            <div className='img-box'>
                {(sex === 'man' && type === 'adult' && front) && <img
                    src={require("../../../assets/images/guide/man_front.svg")}
                    className="body-image man_coords_list1"
                    id="man_coords_list1"
                    useMap="#manFrontGuide"
                    hidefocus="true"
                    alt="男性成人正面"
                />}
                {(sex === 'man' && type === 'adult' && !front) && <img
                    src={require("../../../assets/images/guide/man_behind.svg")}
                    className="body-image man_coords_list2"
                    useMap="#manBehindGuide"
                    id="man_coords_list2"
                    hidefocus="true"
                    alt="男性成人背面"
                />}
                {(sex === 'woman' && type === 'adult' && front) && <img
                    src={require("../../../assets/images/guide/woman_front.svg")}
                    className="body-image woman_coords_list1"
                    useMap="#womanFrontGuide"
                    hidefocus="true"
                    alt="女性成人正面"
                />}
                {(sex === 'woman' && type === 'adult' && !front) && <img
                    src={require("../../../assets/images/guide/woman_behind.svg")}
                    className="body-image woman_coords_list2"
                    useMap="#womanBehindGuide"
                    hidefocus="true"
                    alt="女性成人背面"
                />}
                {(sex === 'man' && type === 'enfant' && front) && <img
                    src={require("../../../assets/images/guide/child_man_front.svg")}
                    className="body-image boy_coords_list1"
                    useMap="#boyFrontGuide"
                    hidefocus="true"
                    alt="男儿童正面"
                />}
                {(sex === 'man' && type === 'enfant' && !front) && <img
                    src={require("../../../assets/images/guide/child_man_behind.svg")}
                    className="body-image boy_coords_list2"
                    useMap="#boyBehindGuide"
                    hidefocus="true"
                    alt="男儿童背面"
                />}
                {(sex === 'woman' && type === 'enfant' && front) && <img
                    src={require("../../../assets/images/guide/child_woman_front.svg")}
                    className="body-image girl_coords_list1"
                    useMap="#girlFrontGuide"
                    hidefocus="true"
                    alt="女儿童正面"
                />}
                {(sex === 'woman' && type === 'enfant' && !front) && <img
                    src={require("../../../assets/images/guide/child_woman_behind.svg")}
                    className="body-image girl_coords_list2"
                    useMap="#girlBehindGuide"
                    hidefocus="true"
                    alt="女儿童背面"
                />}
                <map name="manFrontGuide" id="manFrontGuide">
                    {coordsList[KEYS[0]] && coordsList[KEYS[0]].map((item) => {
                        return <area
                            shape="rect"
                            coords={item.position}
                            key={`man-${item.type}-${item.code}`}
                            alt="男性成人正面"
                            onClick={()=>onBody(item)}
                        />
                    }) }
                </map>
                <map name="manBehindGuide" id="manBehindGuide">
                    {coordsList[KEYS[1]] && coordsList[KEYS[1]].map((item) => {
                        return <area
                            shape="rect"
                            coords={item.position}
                            key={`man-${item.type}-${item.code}`}
                            alt="男性成人背面"
                            onClick={()=>onBody(item)}
                        />
                    }) }
                </map>
                <map name="womanFrontGuide" id="womanFrontGuide">
                    {coordsList[KEYS[2]] && coordsList[KEYS[2]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`woman-${item.type}-${item.code}`}
                                alt="女性成人正面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
                <map name="womanBehindGuide" id="womanBehindGuide">
                    {coordsList[KEYS[3]] && coordsList[KEYS[3]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`woman-${item.type}-${item.code}`}
                                alt="女性成人背面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
                <map name="boyFrontGuide" id="boyFrontGuide">
                    {coordsList[KEYS[4]] && coordsList[KEYS[4]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`boy-${item.type}-${item.code}`}
                                alt="男儿童正面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
                <map name="boyBehindGuide" id="boyBehindGuide">
                    {coordsList[KEYS[5]] && coordsList[KEYS[5]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`boy-${item.type}-${item.code}`}
                                alt="男儿童背面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
                <map name="girlFrontGuide" id="girlFrontGuide">
                    {coordsList[KEYS[6]] && coordsList[KEYS[6]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`girl-${item.type}-${item.code}`}
                                alt="女儿童正面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
                <map name="girlBehindGuide" id="girlBehindGuide">
                    {coordsList[KEYS[7]] && coordsList[KEYS[7]].map((item)=>{
                            return <area
                                shape="rect"
                                coords={item.position}
                                key={`girl-${item.type}-${item.code}`}
                                alt="女儿童背面"
                                onClick={()=>onBody(item)}
                            />
                        })
                    }
                </map>
            </div>
            <div className='back-btns' onClick={onFront}>
                <span>{front?"查看背面":"查看正面"}&nbsp;</span>
                <RightOutlined />
            </div>
        </div>
    )
}

export default BodyParts;
