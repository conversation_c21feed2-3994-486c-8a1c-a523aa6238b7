
/*
 * @Description: 已核身，无代办，跳转进入智能推荐菜单，
 */
import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Row, Col } from 'antd';
import SquareItem from './components/SquareItem';
import Recommend from './components/Recommend';
import OblongItem from './components/OblongItem';
import MinorItem from './components/MinorItem';
import Config from '../../config';
import './index.scss';

const RecomMenu = ({
    dispatch,
    history
}) => {
    
    const toPage = (data) => {
        if(!data.name) return ''
        return data.menu_link;
    }

    return (
        <div className='recommenu-wrapper'>
            <Recommend history={history}/>
            <div className='menu-wrapper '>

                <Row gutter={[20, 24]}>
                    <Col span={12}>
                        <SquareItem 
                            index={1}
                            data={{
                                type: 'register',
                                menu_link: '/register/identity?nextpage=/register/roomlist',
                                name: '当日挂号',
                            }}
                            findUrl={toPage}
                        />
                    </Col>
                    <Col span={12}>
                        <div className='right-menu'>
                            <div className='item'>
                                <OblongItem
                                    index={3}
                                    data={{
                                        type: 'reserve',
                                        menu_link: '/reserve/identity?nextpage=/reserve/date',
                                        name: '预约挂号',
                                    }}
                                    findUrl={toPage}
                                />
                            </div>
                            <div className='item'>
                                <OblongItem
                                    index={6}
                                    data={{
                                        type: 'patient',
                                        menu_link: '/patient/mold',
                                        name: '自助建档',
                                    }}
                                    findUrl={toPage}
                                />
                            </div>
                        </div>
                    </Col>
                </Row>

            </div>
            <ul className='others-menu-wrapper'>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'ticket',
                            menu_link: '/ticket/identity?nextpage=/ticket/mold',
                            name: '凭条补打',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'query',
                            menu_link: '/query/mold',
                            name: '自助查询',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'questionnaire',
                            menu_link: '/questionnaire/identity?nextpage=/questionnaire/home',
                            name: '满意度调查',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem
                        data={{
                            type: 'guide',
                            menu_link: '/guide/home',
                            name: '智能导诊',
                        }} 
                        findUrl={toPage}
                    />
                </li>
                <li className='item-others-menu'>
                    <MinorItem 
                        data={{
                            type: 'report',
                            menu_link: '/report/identity?nextpage=/report/list',
                            name: '报告单打印',
                        }}
                        findUrl={toPage} 
                    />
                </li>
            </ul>
        </div>
    )

}

export default RecomMenu;