const coords_list = {
    man_coords_list1: [
        {
            type: "header",
            position: "110,50,210,95",
            code: "1",
            shape: "rect",
        },
        {
            type: "face",
            position: "100,95,215,170",
            code: "2",
            shape: "rect",
        },
        {
            type: "neck",
            position: "145,170,175,180",
            code: "3",
            shape: "rect",
        },
        {
            type: "chest", //胸部
            position: "110,190,200,270",
            code: "4",
            shape: "rect",
        },
        {
            type: "belly",
            position: "120,275,200,330",
            code: "7",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "70,190,95,380",
            code: "5",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "210,190,245,380",
            code: "6",
            shape: "rect",
        },
        {
            type: "genital",
            position: "120,330,190,360",
            code: "8",
            shape: "rect",
        },
        {
            type: "leg",
            position: "120,360,195,550",
            code: "9",
            shape: "rect",
        },
        {
            type: "foot",
            position: "95,550,220,575",
            code: "10",
            shape: "rect",
        },
    ],
    man_coords_list2: [
        {
            type: "header",
            position: "100,50,215,170",
            code: "11",
            shape: "rect",
        },
        {
            type: "skin",
            position: "115,190,195,325",
            code: "12",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "70,190,110,400",
            code: "13",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "200,190,245,400",
            code: "14",
            shape: "rect",
        },
        {
            type: "leg",
            position: "120,360,195,550",
            code: "15",
            shape: "rect",
        },
        {
            type: "foot",
            position: "95,550,220,575",
            code: "16",
            shape: "rect",
        },
    ],
    woman_coords_list1: [
        {
            type: "header",
            position: "115,60,215,165",
            code: "17",
            shape: "rect",
        },
        {
            type: "neck",
            position: "150,170,175,180",
            code: "18",
            shape: "rect",
        },
        {
            type: "chest", //胸部
            position: "130,180,195,260",
            code: "19",
            shape: "rect",
        },
        {
            type: "belly",
            position: "125,260,200,320",
            code: "20",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "90,210,110,375",
            code: "21",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "200,220,240,375",
            code: "22",
            shape: "rect",
        },
        {
            type: "genital",
            position: "120,325,200,360",
            code: "23",
            shape: "rect",
        },
        {
            type: "leg",
            position: "120,360,195,550",
            code: "24",
            shape: "rect",
        },
        {
            type: "foot",
            position: "115,550,210,570",
            code: "25",
            shape: "rect",
        },
    ],
    woman_coords_list2: [
        {
            type: "header",
            position: "120,60,215,200",
            code: "26",
            shape: "rect",
        },
        {
            type: "skin",
            position: "130,210,210,350",
            code: "27",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "90,210,110,375",
            code: "28",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "200,220,240,375",
            code: "29",
            shape: "rect",
        },
        {
            type: "genital",
            position: "120,325,200,360",
            code: "30",
            shape: "rect",
        },
        {
            type: "leg",
            position: "120,360,195,550",
            code: "31",
            shape: "rect",
        },
    ],
    boy_coords_list1: [
        {
            type: "header",
            position: "95,95,240,250",
            code: "32",
            shape: "rect",
        },
        {
            type: "neck",
            position: "130,255,200,265",
            code: "33",
            shape: "rect",
        },
        {
            type: "chest", 
            position: "120,280,200,320",
            code: "34",
            shape: "rect",
        },
        {
            type: "belly",
            position: "120,325,200,360",
            code: "35",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "55,280,100,380",
            code: "36",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "220,280,260,380",
            code: "37",
            shape: "rect",
        },
        {
            type: "leg",
            position: "115,380,210,490",
            code: "38",
            shape: "rect",
        },
        {
            type: "foot",
            position: "105,500,210,530",
            code: "39",
            shape: "rect",
        },
    ],
    boy_coords_list2: [
        {
            type: "header",
            position: "90,100,230,250",
            code: "40",
            shape: "rect",
        },
        {
            type: "skin", 
            position: "130,260,210,350",
            code: "41",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "60,280,115,380",
            code: "42",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "220,280,260,380",
            code: "43",
            shape: "rect",
        },
        {
            type: "leg",
            position: "115,380,210,490",
            code: "44",
            shape: "rect",
        },
    ],
    girl_coords_list1: [
        {
            type: "header",
            position: "105,95,240,250",
            code: "45",
            shape: "rect",
        },
        {
            type: "neck",
            position: "130,255,200,265",
            code: "46",
            shape: "rect",
        },
        {
            type: "chest", 
            position: "130,280,215,320",
            code: "47",
            shape: "rect",
        },
        {
            type: "belly",
            position: "130,320,220,360",
            code: "48",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "60,270,120,380",
            code: "49",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "230,270,270,380",
            code: "50",
            shape: "rect",
        },
        {
            type: "leg",
            position: "125,400,220,480",
            code: "51",
            shape: "rect",
        },
        {
            type: "foot",
            position: "115,490,220,530",
            code: "52",
            shape: "rect",
        },
    ],
    girl_coords_list2: [
        {
            type: "header",
            position: "105,95,240,250",
            code: "53",
            shape: "rect",
        },
        {
            type: "skin", 
            position: "130,250,215,390",
            code: "54",
            shape: "rect",
        },
        {
            type: "arm_left",
            position: "60,260,120,380",
            code: "55",
            shape: "rect",
        },
        {
            type: "arm_right",
            position: "220,260,270,380",
            code: "56",
            shape: "rect",
        },
        {
            type: "leg",
            position: "130,400,215,480",
            code: "57",
            shape: "rect",
        },
    ],
}

export default {
    coords_list
};