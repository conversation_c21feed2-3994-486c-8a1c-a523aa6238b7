
import React from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import ETable from '../../../components/ETable';

const data = [
    {
        key: '1',
        name: 'X线计算机体层（CT）成像',
        itemId: 32,
        unitPrice: 119.7,
        amount: 1,
        detailCost: 119.7
    },
    {
        key: '2',
        name: '使用螺旋扫描加收',
        itemId: 42,
        unitPrice: 35,
        amount: 1,
        detailCost: 35
    },
    {
        key: '3',
        name: '三维重建加收',
        itemId: 33,
        unitPrice: 45,
        amount: 1,
        detailCost: 45
    },
]

const PayDeatils = ({
    user: { currentUser },
    pay,
    history,
    dispatch,
}) => {

    // 去缴费
    const toPay = (list) => {
        history.push("/pay/confirm")
    }

    return (
        <>
            <ModuleHeader history={history} title={"自助缴费"} />
            <div className="table-center pay-detail-wrapper">
                <ETable
                    tabletype="paydetail"
                    data={data}
                    defaultPage={7}
                    bgText={"缴费详情"}
                    buttonBoxText="我要缴费"
                    buttonBoxShow={true}
                    buttonBoxClick={toPay}
                />
            </div>
        </>
    )
}


export default connect(({ user, pay }) => ({
    user,
    pay
}))(PayDeatils);
