import React, { useState, useEffect } from "react";
import { connect } from 'dva';
import { handlePrint } from './promise';
import { getVoice } from '../../services/state';
import ZfbBrushFace from './ZfbBrushFace';
import OthersPay from './OthersPay';
import ZfbShortcutPay from './ZfbShortcutPay';
import SuccessModal from "../SuccessModal";
import ConfirmModal from '../ConfirmModal';
import gotoMenu from '../.../../../utils/gotoMenu';
import './index.scss';

const PayMode = ({
    history,
    PAYMENT_TYPE,
    SUCCESS_MODAL_TYPE,
    SUCCESS_MODAL_TITLE,
    ...props
}) => {

    const [successModalVisible, setSuccessModalVisible] = useState(false);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);

    useEffect(()=>{
        // getVoice({speakContent: "请选择支付方式"})
    },[])

    // 支付成功提示弹窗
    const handleOpenSuccessModal = (payType) => {
        setSuccessModalVisible(true)
    }

    const handleCloseSuccessModal = () => {
        setSuccessModalVisible(false)
        gotoMenu(history)
    }

    // 是否还有多个取号/缴费 操作 弹窗
    const handleOpenConfirmModal = (item) => {
        setConfirmModalVisible(true)
    }

    const handleCloseConfirmModal = () => {
        setConfirmModalVisible(false);
        handleOpenSuccessModal();
    }

    const onConfirmModalSubmit = () => {
        handleCloseConfirmModal();
        if (PAYMENT_TYPE === "takenum") {
            history.push("/takenum/list");
        } else if (PAYMENT_TYPE === "pay") {
            history.push("/pay/list");
        }
    }

    // 打印
    const onPrint = async () => {
        // const res = await handlePrint({
        //     type: PAYMENT_TYPE==="register" || PAYMENT_TYPE === "reserve" || PAYMENT_TYPE === "takenum" ? 1 : 2 
        // })
        const res = true
        if(res){
            handleOpenSuccessModal();
        }
    }

    return (
        <div className="pay-mode-wrapper">
            {
                window.config.PAY_MODE_TYPE === "faceandsm" ?
                    <div className="faceandsm-wrapper">
                        <ZfbBrushFace
                            history={history}
                            onPrint={onPrint}
                        />
                        <OthersPay
                            history={history}
                            PAYMENT_TYPE={PAYMENT_TYPE}
                            onPrint={onPrint}
                            handleOpenConfirmModal={handleOpenConfirmModal}
                        />
                    </div> :
                    <div className="sm-wrapper">
                        {/* <ZfbShortcutPay
                            history={history}
                            PAYMENT_TYPE={PAYMENT_TYPE}
                            handleOpenSuccessModal={handleOpenSuccessModal}
                            handleOpenConfirmModal={handleOpenConfirmModal}
                        /> */}
                        <OthersPay
                            history={history}
                            PAYMENT_TYPE={PAYMENT_TYPE}
                            onPrint={onPrint}
                            handleOpenConfirmModal={handleOpenConfirmModal}
                        />
                    </div>

            }
            {successModalVisible &&
                <SuccessModal
                    modalVisible={successModalVisible}
                    onCancel={handleCloseSuccessModal}
                    modalType={SUCCESS_MODAL_TYPE}
                    title={SUCCESS_MODAL_TITLE}
                />
            }
            {/* {confirmModalVisible &&
                <ConfirmModal
                    modalVisible={confirmModalVisible}
                    title={PAYMENT_TYPE === "takenum" ? "您还有待取号记录，是否继续取号" : PAYMENT_TYPE === "pay" ? "您还有待缴费项目，是否继续缴费" : ""}
                    okText={"继续"}
                    cancelText={"取消"}
                    onCancel={handleCloseConfirmModal}
                    onConfirm={onConfirmModalSubmit}
                />
            } */}
        </div>
    )

}

export default connect(({ user, pay }) => ({
    user,
    pay,
}))(PayMode);