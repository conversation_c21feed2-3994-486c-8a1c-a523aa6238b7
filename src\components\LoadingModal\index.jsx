/*
 * @Description: 加载弹窗
 */
import React from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const LoadingModal = (msg) => {
	const Wrapp = ({
		title
	}) => {
		return (
			<div className='loading-tip-modal-wrap'>
				<img src={require("../../assets/images/modal/loading.png")} alt="加载图标" />
				<p className='tip-text'>{title}</p>
			</div>
		)
	
	}
	
	let _modal = null

	_modal = Modal.error({
		width: 846,
		centered: true,
		closable: true,
		maskClosable: true,
		icon: <ModalCloseItem num={window.config.TIP_OVER_TIME} onCancel={()=>{
			_modal && _modal.destroy();
			_modal = null;
		}}/>,
		content: <Wrapp title={msg}/>,
		getContainer: document.getElementById("_module"),

	})

	return _modal
}

export default LoadingModal