/*
 * @Description: 
    核身介质弹窗
 */
import React, { useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import { getVoice } from '../../services/state';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const modalTypeText = [
    "请将身份证放置感应区",
    "请插入社保卡",
    "请扫描医保电子凭证二维码", 
    "请在机器右侧医保终端刷脸登录",
    "请扫描电子健康卡二维码",
    "请插入就诊卡", 
    "请取走您的社保卡或就诊卡", 
    "请将外国人永居证放置感应区", 
    "请扫描回执单或小票的门诊号条形码"
]

// 提示弹窗图片地址string
const modalTypeImgs = [
    "type-idCard.gif",
    "type-socialCard.gif",
    "type-elMedCard.gif", 
    "type-face.gif",    
    "type-dzjk.gif",
    "type-jzk.gif", 
    "type-return-card.gif", 
    "type-fr-idCard.gif", 
    "type-scan-code.gif"];

const TypeModal = ({
    modalVisible,
    modalType,
    onCancel,
}) => {

    useEffect(()=>{
        // getVoice({speakContent: modalTypeText[modalType]})
    },[])

    return (
        <Modal 
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={false}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper identity-type-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.READ_CARD_OVER_TIME}/>
            <h2 className='tip-text'>{modalTypeText[modalType]}</h2>
            <div className='tip-img-wrapper'>
                {
                    modalTypeImgs[modalType] && <img src={require("../../assets/images/gifs/"+modalTypeImgs[modalType])} alt="核身提示图片"/>
                }
            </div>
        </Modal>
    )
}

export default TypeModal;