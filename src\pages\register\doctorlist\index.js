import React from 'react';
import moment from 'moment';
import DoctorPage from '../../../components/RegDoctorPage';

const RegDoctorList = (props) => {

    const {
        history
    } = props;


    return (
        <DoctorPage
            history={history}
            PAGE_NAME={"当日挂号"}
            PAGE_STEP={1}
            PAGE_TYPE={"register"}
            queryParams={{
                beginTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            }}
            nextRouteName="confirm"
        />
    )
}

export default RegDoctorList;