/*
 * @Description: 排班号源界面
 */
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getVoice } from '../../services/state';
import ModuleHeader from '../ModuleHeader';
import RegSteps from '../Steps';
import EList from '../EList';
import moment from 'moment';
import './index.scss';


const timeRanges = [{
    name: "7",
    active: true,
    num: [
        '07:00~07:10',
        '07:10~07:20',
        '07:20~07:30',
        '07:30~07:40',
        '07:40~07:50',
        '07:50~08:00',
    ],
},{
    name: "8",
    active: false,
    num: [
        '08:00~08:10',
        '08:10~08:20',
        '08:20~08:30',
        '08:30~08:40',
        '08:40~08:50',
        '08:50~09:00',
    ]
},{
    name: "9",
    active: false,
    num: [
        '09:00~09:10',
        '09:10~09:20',
        '09:20~09:30',
        '09:30~09:40',
        '09:40~09:50',
        '09:50~10:00',
    ]
},{
    name: "10",
    active: false,
    num: [
        '10:00~10:10',
        '10:10~10:20',
        '10:20~10:30',
        '10:30~10:40',
        '10:40~10:50',
        '10:50~11:00',
    ]
},{
    name: "11",
    active: false,
    num: [
        '11:00~11:10',
        '11:10~11:20',
        '11:20~11:30',
        '11:30~11:40',
        '11:40~11:50',
        '11:50~12:00',
    ]
},{
    name: "13",
    active: false,
    num: [
        '13:00~13:10',
        '13:10~13:20',
        '13:20~13:30',
        '13:30~13:40',
        '13:40~13:50',
        '13:50~14:00',
    ]
},{
    name: "14",
    active: false,
    num: [
        '14:00~14:10',
        '14:10~14:20',
        '14:20~14:30',
        '14:30~14:40',
        '14:40~14:50',
        '14:50~15:00',
    ]
},{
    name: "15",
    active: false,
    num: [
        '15:00~15:10',
        '15:10~15:20',
        '15:20~15:30',
        '15:30~15:40',
        '15:40~15:50',
        '15:50~16:00',
    ]
},{
    name: "16",
    active: false,
    num: [
        '16:00~16:10',
        '16:10~16:20',
        '16:20~16:30',
        '16:30~16:40',
        '16:40~16:50',
        '16:50~17:00',
    ]
},{
    name: "17",
    active: false,
    num: [
        '17:00~17:10',
        '17:10~17:20',
        '17:20~17:30',
        '17:30~17:40',
        '17:40~17:50',
        '17:50~18:00',
    ]
}]

const NumPage = ({
    user: { currentUser },
    register,
    history,
    PAGE_NAME,
    PAGE_STEP,
    PAGE_TYPE,
    nextRouteName,
    queryParams,
    dispatch,
}) => {
    const [timeRange, setTimeRange] = useState(timeRanges);
    const [selectRange, setSelectRange] = useState(0);

    useEffect(()=>{
        // getVoice({speakContent: "请选择号源"})
    },[])

    const getData = () => {
        let newarr = [...timeRange];
        const nowHour = moment().format("H");
        newarr.forEach((item,index)=>{
            item.active = true;
            // if(parseInt(item.name) < parseInt(nowHour)){
            //     item.active = true;
            // } else {
            //     item.active = true;
                // if(item.name === nowHour){
                //     setSelectRange(index)
            //     // }
            // }
        })
        setTimeRange(newarr)
    }

    const goNext = (item) => {
        dispatch({
            type: 'register/setSelectOrderNum',
            payload: {
                selectOrderNum: {
                    time: item,
                },
            }
        })
        history.push(`/${PAGE_TYPE}/${nextRouteName}`)  
    }

    const onTime = (index)=>{
        setSelectRange(index)
    }

    useEffect(()=>{
        getData(timeRanges)
    },[])

    
    return (
        <>
            <ModuleHeader history={history} title={PAGE_NAME} />
            <RegSteps current={PAGE_STEP} type={PAGE_TYPE} /> 
            <div className='number-wrapper width768'>
                <h2 className="time-title">选择时段</h2>
                <ul className='time-wrapper'>
                    {
                        timeRange.map((item, index)=>{
                            return (
                                item.active ? 
                                <li className={selectRange===index?"time-item actived":"time-item"} onClick={()=>onTime(index)} key={index}>
                                    <span>{item.name}时</span>
                                </li>:
                                <li className="time-item zh" key={index}>
                                    <span>{item.name}时</span>
                                </li>
                            )
                        })
                    }
                </ul>
                <h2 className="time-title">选择号源</h2>
                <ul className='range-wrapper'>
                    {
                        timeRange[selectRange].num.map((item, index)=>{
                            return <li className='range-item' onClick={()=>goNext(item)} ><span>{item}</span> </li>
                        })
                    }
                </ul>
            </div>
        </>
    )
}


export default connect(({ user, register }) => ({
    user,
    register
}))(NumPage);
