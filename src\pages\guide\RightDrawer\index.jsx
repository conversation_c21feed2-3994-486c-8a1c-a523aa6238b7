
import React, { useState } from 'react';
import { Drawer, Row, Col, } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import './index.scss';


const RightDrawer = ({
    visible,
    data,
    onClose,
    toConclusion,
}) => {

    return (
        <Drawer
            title={null}
            closable={false}
            maskClosable={true}
            onClose={onClose}
            visible={visible}
            width={488}
            height={850}
            placement={"right"}
            key={"right-drawer"}
            className="right-drawer"
            getContainer={document.getElementById("guide-page")}
        >
            <h3 className='title-box'>
                <span>{data?.parts??""}</span>
                <img className='close' src={require("../../../assets/images/btns/circle-close.png")} alt="" onClick={onClose}/>
            </h3>
            <Row gutter={[0,25]}>
                <Col span={24}>
                    <div className='item' onClick={()=>toConclusion({})}>
                        <span>头晕</span>
                        <span style={{color: "#1677FF"}}><RightOutlined /></span>
                    </div>
                </Col>
                <Col span={24}>
                    <div className='item'>
                        <span>头痛</span>
                        <span style={{color: "#1677FF"}}><RightOutlined /></span>
                    </div>
                </Col>
            </Row>
        </Drawer>
    )

}

export default RightDrawer;