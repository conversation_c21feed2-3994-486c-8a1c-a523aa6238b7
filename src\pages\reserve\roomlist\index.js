
import React from 'react';
import moment from 'moment';
import RoomPage from '../../../components/RegRoomPage';

const ResRoomList = (props) => {

    const {
        history
    } = props;


    return (
        <RoomPage
            PAGE_NAME={"预约挂号"}
            PAGE_TYPE={"reserve"}
            PAGE_STEP={1}
            nextRouteName={"doctorlist"}
            history={history}
            queryParams={{
                registrationType: "0",
                registrationDate: moment().format('YYYY-MM-DD')
            }}
        />
    )
}

export default ResRoomList;