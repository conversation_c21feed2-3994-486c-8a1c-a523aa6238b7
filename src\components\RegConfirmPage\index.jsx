import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import ModuleHeader from '../ModuleHeader';
import ConfirmInfoList from '../ConfirmInfoList';
import PayMode from '../PayMode';
import BlueBgTitle from '../BlueBgTitle';
import "./index.scss";

const ConfirmPage = ({
    user: { currentUser },
    register: { selectDoc, selectDept, selectOrderNum },
    history,
    PAGE_NAME,
    PAGE_STEP,
    PAGE_TYPE,
    nextRouteName,
    queryParams,
    dispatch,
}) => {

    const gutter = window.config.PAY_MODE_TYPE === "faceandsm" ? [24, 0] : [0, 44];


    return (
        <>
            <ModuleHeader history={history} title={PAGE_NAME} />
            <div className='reg-confrim-wrapper confirm-page-wrapper'>
                <Row gutter={gutter}>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <div className={window.config.PAY_MODE_TYPE==="faceandsm" ? "confirm-info-list-wrapper col12" : "confirm-info-list-wrapper col24"}>
                            <div className='circle-box left'></div>
                            <div className='circle-box right'></div>
                            <BlueBgTitle title={"挂号单"} />
                            <div className='info-bold-box'>
                                <h2 className='info-bold'>患者姓名：张三</h2>
                                <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
                            </div>
                            <Row gutter={[0, 8]}>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>医保金额</span>
                                        <span className='black-span'>10元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>总金额</span>
                                        <span className='black-span'>{selectDoc.price}元</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号科室</span>
                                        <span className='black-span'>{selectDept.deptName}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>挂号类别</span>
                                        <span className='black-span'>{selectDoc.doctorType}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>主治医生</span>
                                        <span className='black-span'>{selectDoc.doctorName}</span>
                                    </div>
                                </Col>
                                <Col span={24} className='item-message-col'>
                                    <div className='item-message'>
                                        <span>就诊时间</span>
                                        <span className='black-span'>13:30～13:50</span>
                                    </div>
                                </Col>
                            </Row>
                        </div>
                    </Col>
                    <Col span={window.config.PAY_MODE_TYPE==="faceandsm" ? 12 : 24}>
                        <PayMode
                            history={history}
                            currentUser={currentUser}
                            dispatch={dispatch}
                            SUCCESS_MODAL_TYPE={0}
                            SUCCESS_MODAL_TITLE={"挂号成功，请取走凭条"}
                            PRINT_ERROR_MODAL_TYPE={0}
                            PAYMENT_TYPE="register"
                        />
                    </Col>
                </Row>
            </div>
        </>
    )

}

export default connect(({ user, register }) => ({
    user,
    register
}))(ConfirmPage);