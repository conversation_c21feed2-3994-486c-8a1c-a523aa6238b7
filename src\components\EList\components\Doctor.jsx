import React from 'react';

const Doctor = ({
    list,
    selectDept,
    handleClick
})=>{

    const onReg = (data) => {
        if(!data.schedul){
            return;
        }
        handleClick(data)
    }

    return (
        <ul className='doctor-list-wrapper'>
            {
                list?.map((item,index)=>{
                    return (
                        <li className={item?.schedul ? "doctor-item" : "doctor-item no-schedul"} key={item.doctorID} onClick={() => onReg(item)}>
                            <div className='info'>
                                <img src={require("../../../assets/images/others/doctor.png")} alt="医生照片" />
                                <div className='right'>
                                    <p className='name'>{item.doctorName}{"[" + item.jobName + "]"}</p>
                                    <p>科室：<b>{selectDept?.deptName}</b></p>
                                    <p>排班：<b>{item.scheduling}</b></p>
                                    <p>挂号费：<span className='money'>{item.price + "元"}</span></p>
                                    <p>余号：{item.schedul}</p>
                                </div>
                            </div>
                            <div className="reg-button-wrapper" >
                                <span>{item.schedul==="0"||item.schedul===0?"无号":"挂号"}</span>
                            </div>
                        </li>
                    )
                })
            }
        </ul>
    )

}

export default Doctor;