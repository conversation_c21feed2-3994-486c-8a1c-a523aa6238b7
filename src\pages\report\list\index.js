
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { printReport } from '../../../services/print'; 
import ModuleHeader from '../../../components/ModuleHeader';
import InfoModal from '../../../components/InfoModal';
import ETable from '../../../components/ETable';
import gotoMenu from '../../../utils/gotoMenu';
import PrintModal from '../../../components/PrintModal';
import SuccessModal from '../../../components/SuccessModal';


const data = [
    {
        key: '1',
        inspectionItem: '自费项目自费项目',
        requestOdd: 23,
        verifyTime: "2022-09-27",
        count: 0,
    },
    {
        key: '2',
        inspectionItem: '自费项目',
        requestOdd: 42,
        verifyTime: "2022-09-27",
        count: 0,
    },
    {
        key: '3',
        inspectionItem: '自费项目',
        requestOdd: 33,
        verifyTime: "2022-09-27",
        count: "0",
    },
];

/**
 * 打印报告
 * @param fields 
*/

const handlePrintReport = async fields => {
    try {
        const response = await printReport(fields);
        if (response.code === '0') {
            return true
        }
        return false;
    } catch (error) {
        return false;
    }
};

const ReportList = ({
    user: { currentUser },
    pay,
    history,
    dispatch,
}) => {

    const [reportList, setReportList] = useState([...data]);
    const [allChecked, setAllChecked] = useState(true);
    const [printModalVisible, setPrintModalVisible] = useState(false);
    const [successModalVisible, setSuccessModalVisible] = useState(false);


    useEffect(()=>{
        if(data.length===0){
            InfoModal("暂无可打印的报告单")
            gotoMenu(history)
        }

        const arr = data.map((item)=>{
            return {
                ...item, 
                checked: true
            }
        })
        setReportList(arr)
    },[])


    // 全选
    const onAllChecked = () => {
        let arr = [...reportList];
        arr.forEach((item) => {
            item.checked = allChecked ? false : true;
        })
        setAllChecked(!allChecked);
        setReportList(arr);
    }

    // 单选
    const onCheck = (record) => {
        let arr = [...reportList];
        arr.forEach((item) => {
            if (record.requestOdd === item.requestOdd) {
                item.checked = !item.checked;
            }
        })
        let result = arr.every((item) => {
            return item.checked === true;
        })

        setReportList(arr);
        setAllChecked(result);
    }

    // 去缴费
    const toPrint = async() => {
        let arr = reportList.filter(((item) => item.checked))
        if(arr.length===0){
            InfoModal("请选择需要打印的项目")
            return;
        }

        // 打印接口
        
        // handleOpenPrintModal()

        // setTimeout(()=>{
        //     handleClosePrintModal();
        //     handleOpenSuccessModal();
        // },5000)

        // const res = await handlePrintReport();
        // if(res){
        //     handleOpenSuccessModal();
        // }
        handleOpenSuccessModal();
    }

    // 打印提示弹窗
    const handleOpenPrintModal = () => {
        setPrintModalVisible(true)
    }

    const handleClosePrintModal = () => {
        setPrintModalVisible(false)
    }

    // 打印成功提示
    const handleOpenSuccessModal = () => {
        setSuccessModalVisible(true)
    }

    const handleCloseSuccessModal = () => {
        setSuccessModalVisible(false)
        gotoMenu(history)
    }

    return (
        <>
            <ModuleHeader history={history} title={"报告打印"} />
            <div className="table-center report-list-wrapper">
                <ETable
                    tabletype="reportlist"
                    data={reportList}
                    defaultPage={7}
                    bgText={"打印单"}
                    buttonBoxText="我要打印"
                    buttonBoxShow={true}
                    allChecked={allChecked}
                    buttonBoxClick={toPrint}
                    onCheck={onCheck}
                    onAllChecked={onAllChecked}
                />
            </div>
            {
                printModalVisible && <PrintModal
                    modalVisible={printModalVisible}
                    onCancel={handleClosePrintModal}
                    title={"正在打印第1份报告单，共3份"}
                    subTitle={"尚有1份未出结果"}
                />
            }
            {
                successModalVisible && <SuccessModal 
                    modalVisible={successModalVisible}
                    onCancel={handleCloseSuccessModal}
                    modalType={"A4"}
                    title={"打印成功，请取走报告单"}
                />
            }
        </>
    )
}


export default connect(({ user }) => ({
    user
}))(ReportList);
