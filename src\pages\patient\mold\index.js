
import React from 'react';
import { connect } from 'dva';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';

const PatientMold = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const toAdult = () => {
        history.push(`/patient/identity?nextpage=/patient/create`)
    }

    const toEnfant = () => {
        history.push(`/patient/identity?nextpage=/patient/enfant`)
    }
    
    return (
        <>
            <ModuleHeader history={history} title={"自助建档"}/>
            <RegSteps current={0} type="patient" />
            <ul className="mold-wrapper create">
                <li className="item bggreen" onClick={toAdult}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/user-icon.png")} alt="成人建档图标"/>
                    </div>
                    <p className="title">本人</p>
                </li>
                <li className="item bgblue" onClick={toEnfant}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/enfant-icon.png")} alt="儿童建档图标"/>
                    </div>
                    <p className="title">儿童</p>
                </li>
            </ul>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(PatientMold);
