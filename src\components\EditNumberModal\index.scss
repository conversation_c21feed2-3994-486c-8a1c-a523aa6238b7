.edit-number-modal{
    .ant-modal-content{
        border-radius: 10px;
    }
    .ant-modal-body{
        min-height: 635px;
        padding: 18px 0 0 ;
        @media screen and (min-width:1280px) {
          min-height: 430px;
        }
    }
    // .ant-modal-close{
    //     background-image: linear-gradient(#4BAEFF, #288DE5);
    //     color: #fff;
    //     font-size: 50px;
    //     border-radius: 0 8px 0 15px;
    //     .ant-modal-close-x{
    //         font-size: 34px;
    //     }
    // }
    .confirm-warp{
        padding: 50px 100px 0px;
        text-align: center;
        .confirm-col{
            display: flex;
            justify-content: space-between;
            font-size: 25px;
            margin-bottom: 10px;
        }
    }
    .confirm-btn{
        width: 93px;
        height: 52px;
        color: #ffffff;
        font-size: 18px;
        background-image: linear-gradient(#4BAEFF, #288DE5);
        border: none;
        border-radius: 15px;
        margin-top: 20px;
    }
    .title {
        color: #4D9BFA;
        font-size: 50px;
        line-height: 65px;
        text-align: center;
        margin-bottom: 24px;
        @media screen and (min-width:1280px) {
            font-size: 40px;
            line-height: 50px;
            margin-bottom: 15px;
        }
    }
    .info-wrapper {
        // margin: 30px 0 0;
        // font-size: 20px;
        // border: 3px solid #4D9BFA;
        border-radius: 15px;
        padding: 15px 8px 4px;
        .info-col {
            display: flex;
            justify-content: space-between;
            font-size: 35px;
            margin-bottom: 10px;
            .info-title{
                color: #A7A7A7;
            }
            .info-content {
                width: 100%;
                .rmb {
                    color: #ff0000;
                }
    
                .ant-input {
                    // width: 100%;
                    font-size: 35px;
                    text-align: left;
                    padding: 8px 20px;
                    border-color: #1677FF;
                    border: 2px solid #1677FF;
                    @media screen and (min-width:1280px) {
                        font-size: 30px;
                        padding: 6px 20px;
                    }
                }
            }
        }
        .keyboard-wrap{
            position: relative;
            // width: 450px;
            width: 100%;
            border-radius: 15px;
            box-shadow: 0px 0px 10px rgba(0,0,0,0.16);
            padding: 20px;
            margin: 10px auto;
            background-color: #E3EEFF;
            @media screen and (min-width:1280px) {
                padding: 10px;
                padding-right: 4px;
            }
        }
    }
}