/*
 * @Description:
    操作成功弹窗
 */
import React from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

// const modalTypeText = ["挂号成功，请取走凭条", "预约成功", "缴费成功，请取走凭条", "签到成功，请取走凭条", "充值成功，请取走凭条", "开单成功，请取走凭条", "打印成功，请取走清单", "取号成功，请取走凭条"]

const SuccessModal = ({
    modalVisible,
    modalType,
    onCancel,
    title,
}) => {

    return (
        <Modal 
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={true}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper success-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.TIP_OVER_TIME}/>
            <div className='modal-wrapper'>
                <img className="modal-img" src={require("../../assets/images/modal/success-sm.png")} alt="成功" />
                <h2 className='modal-text'>{title}</h2>
            </div>
            <div className='modal-img-box'>
                {
                    modalType==="A4" ? 
                    <img src={require("../../assets/images/modal/report-print-modal-success.png")} alt="" />:
                    <img src={require("../../assets/images/gifs/print-ticket-tips.gif")} alt="" />
                }
            </div>
        </Modal>
    )

}

export default SuccessModal;