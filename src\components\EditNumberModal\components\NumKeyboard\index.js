/*
 * @Author: lhy
 * @Date: 2022-07-20 15:04:30
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2022-08-10 10:54:20
 * @FilePath: \自助机web版\src\components\NumKeyboard\index.js
 * @Description: 
 */
import React from 'react';
import {Row, Col,} from 'antd';
import './index.scss';

const btnArray = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '删除' ,'0', '确认'];

const NumKeyboard = (props) => {
    return (
        <Row className="number-keyboard" gutter={[10, 10]}>
            {
                btnArray.map((item, index) => {
                    return (
                        <Col span={8} key={index}>
                            {
                                <div 
                                    className="number-keyboard-btn"
                                    onClick={() => {
                                        if(item === '删除'){
                                            props.getBackspace()
                                        }else if(item === '确认'){
                                            props.getConfirm()
                                        }else {
                                            props.getKey(btnArray[index])
                                        }
                                    }}
                                >
                                    {item}
                                </div>
                            }
                        </Col>
                    )
                })
            }
        </Row>
    )
};

export default NumKeyboard;