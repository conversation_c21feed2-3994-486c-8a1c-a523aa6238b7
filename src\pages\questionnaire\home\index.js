
import React, { useState } from 'react';
import { connect } from 'dva';
import InfoModal from '../../../components/InfoModal';
import ModuleHeader from '../../../components/ModuleHeader';
import './index.scss';

const QuestionnaireHome = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const [checked, setChecked] = useState(false);


    const toList = () => {
        if(!checked){
            InfoModal("请先阅读并同意上述问卷须知");
            return;
        }
        history.push(`/questionnaire/list`)
    }

    const onChange = () => {
        setChecked(!checked)
    }

    return (
        <>
            <ModuleHeader history={history} title={"满意度调查"}/>
            <div className='questionnaire-home-wrapper'>
                <h4 className='title'>欢迎参加</h4>
                <h3 className='subtitle'>满意度调查</h3>
                <div className='content'>
                    <p className='content-title'>尊敬的患者/家属您好：</p>
                    <p>
                        为提高医院的服务质量，加强医院行风建设，充分行使您的
                        监督权，希望通过您的真是回答反映出您对我院工作的意见
                        和建议。我们将严格保护您的信息，不会泄露您的个人信息。
                        十分感谢您的支持与参与！
                    </p>
                </div>
                <div className='bottom-btns'>
                    <div className='btn-box' onClick={toList}><span>开始答题</span></div>
                    <div className='agree-box' onClick={onChange}>
                        {
                            checked ? 
                            <img width={28} height={28} src={require("../../../assets/images/btns/checked.png")} alt=""/>:
                            <img width={28} height={28} src={require("../../../assets/images/btns/no-check.png")} alt=""/>
                        }
                        <span>我已阅读并同意上述问卷须知</span>
                    </div>
                </div>
            </div>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(QuestionnaireHome);
