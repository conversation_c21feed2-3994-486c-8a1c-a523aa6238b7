
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getVoice } from '../../services/state';
import ModuleHeader from '../ModuleHeader';
import IndexBar from '../IndexBar';
import RegSteps from '../Steps';
import SearchInput from '../SearchInput';
import EList from '../EList';
import './index.scss';


const roomData = [
    {
        "deptName":"妇科门诊",
        "deptID":"148",
    },
    {
        "deptName":"产科门诊",
        "deptID":"621",
    },
    {
        "deptName":"计划生育科",
        "deptID":"998",
    },
    {
        "deptName":"妇女保健科",
        "deptID":"898",
    },
    {
        "deptName":"新生儿科",
        "deptID":"152",
    },
    {
        "deptName":"普通内科",
        "deptID":"150",
    },
    {
        "deptName":"中医妇科",
        "deptID":"346",
    },
    {
        "deptName":"生殖门诊",
        "deptID":"342",
    },
    {
        "deptName":"乳腺外科",
        "deptID":"404",
    },
    {
        "deptName":"肿瘤放疗科",
        "deptID":"405",
    },
    {
        "deptName":"遗传咨询",
        "deptID":"993",
    },
    {
        "deptName":"麻醉疼痛门诊",
        "deptID":"1060",
    },
    {
        "deptName":"男性科",
        "deptID":"149",
    },
    {
        "deptName":"泌尿外科",
        "deptID":"899",
    },
    {
        "deptName": "骨科",
        "deptID": "823"
    },
    {
        "deptName": "儿科",
        "deptID": "213",
    },
    {
        "deptName": "生长发育门诊",
        "deptID": "222",
    },
    {
        "deptID": "3551",
        "deptName": "急诊内科",
    },
    {
        "deptID": "3552",
        "deptName": "急诊儿科",
    },
    {
        "deptID": "3553",
        "deptName": "急诊外科",
    },
    {
        "deptID": "3531",
        "deptName": "中医肿瘤",
    },
    {
        "deptID": "3532",
        "deptName": "中医内科",
    },
    {
        "deptID": "3533",
        "deptName": "中医妇科",
    },
    {
        "deptID": "3534",
        "deptName": "中医皮肤科",
    },
    {
        "deptID": "3511",
        "deptName": "胸外科",
    },
    {
        "deptID": "3512",
        "deptName": "骨科",
    },
    {
        "deptID": "3518",
        "deptName": "康复门诊",
    },
    {
        "deptID": "3513",
        "deptName": "普外科",
    },
    {
        "deptID": "3514",
        "deptName": "耳鼻喉科",
    },
    {
        "deptID": "3515",
        "deptName": "眼科",
    },
    {
        "deptID": "3516",
        "deptName": "口腔科",
    },
    {
        "deptID": "3517",
        "deptName": "皮肤科",
    },
    {
        "deptID": "3501",
        "deptName": "心血管内科",
    },
    {
        "deptID": "3502",
        "deptName": "呼吸内科",
    },
    {
        "deptID": "3503",
        "deptName": "消化内科",
    },
    {
        "deptID": "3504",
        "deptName": "肿瘤内科",
    },
    {
        "deptID": "3505",
        "deptName": "肝胆内科",
    },
    {
        "deptID": "3506",
        "deptName": "普内科",
    },
    {
        "deptID": "3507",
        "deptName": "心理卫生科",
    },
]

const RoomPage = ({
    user: { currentUser },
    register,
    history,
    PAGE_NAME,
    PAGE_STEP,
    PAGE_TYPE,
    nextRouteName,
    queryParams,
    dispatch,
}) => {

    const [searchValue , setSearchValue] = useState('');
    const [data, setData] = useState(roomData);

    useEffect(()=>{
        // getVoice({speakContent: "请选择科室"})
    },[])

    const onSearchInputChange = (e) => {
        if(e.target.value === ''){
            setData(roomData)
        }
        setSearchValue(e.target.value)
        
    }

    const onSearch = () => {
        if(!searchValue) return
        console.log(searchValue);
        const data = roomData.filter((item) => item.deptName.indexOf(searchValue) > -1)
        if(data.length > 0){
            setData(data)
        }
        
    }

    const goNext = (item) => {
        dispatch({
            type: "register/setSelectDept",
            payload: {
                selectDept: {
                    ...item
                }
            },
        })
        history.push(`/${PAGE_TYPE}/${nextRouteName}`)  
    }
    
    return (
        <>
            <ModuleHeader history={history} title={PAGE_NAME} />
            <RegSteps current={PAGE_STEP} type={PAGE_TYPE} /> 
            <div className='room-wrapper'>
                {/* <IndexBar /> */}
                <SearchInput placeholder={"点击快速搜索科室、专家"} value={searchValue} onCharge={onSearchInputChange} onSearch={onSearch}/>
                <EList 
                    itemtype="ROOM"
                    data={data}
                    defaultPage={12}
                    handleClick={goNext}
                />
            </div>
        </>
    )
}


export default connect(({ user, register }) => ({
    user,
    register
}))(RoomPage);
