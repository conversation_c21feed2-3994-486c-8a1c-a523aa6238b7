import React from 'react';
import { Row, Col } from 'antd';
import BlueBgTitle from '../BlueBgTitle';
import "./index.scss";

const RegItem = ({
    data,
    currentUser,
}) => {
    return (
        <>
            <div className='info-bold-box'>
                <h2 className='info-bold'>患者姓名：张三</h2>
                <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
            </div>
            <Row gutter={[0, 8]}>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>医保金额</span>
                        <span className='black-span'>10元</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>总金额</span>
                        <span className='black-span'>12元</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>挂号科室</span>
                        <span className='black-span'>内科</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>挂号类别</span>
                        <span className='black-span'>普通</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>主治医生</span>
                        <span className='black-span'>普通医生</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>就诊时间</span>
                        <span className='black-span'>13:30～13:50</span>
                    </div>
                </Col>
            </Row>

        </>
    )
}

const ResItem = ({
    data,
    currentUser,
    onResConfirm
}) => {
    return (
        <>
            <div className='info-bold-box'>
                <h2 className='info-bold'>患者姓名：张三</h2>
                <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
            </div>
            <Row gutter={[0, 8]}>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>医保金额</span>
                        <span className='black-span'>10元</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>总金额</span>
                        <span className='black-span'>12元</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>挂号科室</span>
                        <span className='black-span'>内科</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>挂号类别</span>
                        <span className='black-span'>普通</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>主治医生</span>
                        <span className='black-span'>普通医生</span>
                    </div>
                </Col>
                <Col span={24} className='item-message-col'>
                    <div className='item-message'>
                        <span>就诊时间</span>
                        <span className='black-span'>13:30～13:50</span>
                    </div>
                </Col>
            </Row>
            <div className='info-button' onClick={()=>onResConfirm()}><span>确认</span></div>
        </>
    )
}

const PayItem = ({
    data,
    currentUser,
}) => {
    return (<>
        <div className='info-bold-box'>
            <span className='info-bold'>患者姓名：张三</span>
            <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
        </div>
        <Row gutter={[0, 8]}>
            <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>医保金额</span>
                    <span className='black-span'>10元</span>
                </div>
            </Col>
            <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>总金额</span>
                    <span className='black-span'>12元</span>
                </div>
            </Col>
            {/* <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>项目名称</span>
                    <span className='black-span'>（自费）核酸开单混采</span>
                </div>
            </Col> */}
        </Row>
    </>)
}

const NucleinItem = ({
    data,
    currentUser,
}) => {
    return (<>
        <div className='info-bold-box'>
            <span className='info-bold'>患者姓名：张三</span>
            <h2 className='info-bold'>自费金额：<span className='red-text'>2元</span></h2>
        </div>
        <Row gutter={[0, 8]}>
            <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>医保金额</span>
                    <span className='black-span'>10元</span>
                </div>
            </Col>
            <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>总金额</span>
                    <span className='black-span'>12元</span>
                </div>
            </Col>
            {/* <Col span={24} className='item-message-col'>
                <div className='item-message'>
                    <span>项目名称</span>
                    <span className='black-span'>（自费）核酸开单混采</span>
                </div>
            </Col> */}
        </Row>
    </>)
}

const ConfirmInfoList = ({
    type,
    data,
    title,
    currentUser,
    handleOpenMoneyDrawer,
    accountMoney,  // //充值金额
}) => {

    return (
        <div className={"confirm-info-list-wrapper " + type}>
            <div className='circle-box left'></div>
            <div className='circle-box right'></div>
            <BlueBgTitle title={title} />
            {
                type === "register" || type === "takenum"?
                <RegItem data={data} currentUser={currentUser}/> :
                type === "reserve"?
                <ResItem data={data} currentUser={currentUser}/> :
                type === "pay"?
                <PayItem data={data} currentUser={currentUser}/> : 
                type === "nuclein"?
                <NucleinItem data={data} currentUser={currentUser}/> : 
                <></>
            }
        </div>
    )
}


export default ConfirmInfoList;