
import React, { useEffect } from 'react';
import { connect } from 'dva';
import { getVoice } from '../../../services/state';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';
import moment from 'moment';
import './index.scss';

const RecommDatePage = ({
    history,
    dispatch,
}) => {
    const { RESERVE_DAY_COUNT } = window.config;

    useEffect(()=>{
        // getVoice({speakContent: "请选择日期"})
    },[])

    const dayArray = [];
    for(let i = 0; i < RESERVE_DAY_COUNT; i++){
        dayArray.push({
            date: moment().add(i,'day').format('YYYY-MM-DD'),
            week: i===0?"今天":i===1?"明天":moment().add(i,'day').format('dddd'),
            nowDate: i===0?true:false,
        })
    };

    const goNext = (item) => {
        dispatch({
            type: "register/setSelectOrderNum",
            payload: {
                selectOrderNum: {
                    date: item.date
                }
            },
        })
        history.push("/recommend/numberlist")
    }

    return (
        <>
            <ModuleHeader history={history} title={"预约挂号"} />
            <RegSteps current={0} type={"recommend"} />
            <ul className='reserve-date-wrapper'>
                { dayArray.map((item,index)=>{
                    return (
                        <li key={index} className={item.nowDate?"date-item selected":"date-item"} onClick={()=>goNext(item)}>
                            <span className="text1">{item.week}</span>
                            <span className="text2">{item.date}</span>
                        </li>
                    )
                })}
            </ul>
        </>
    )
}


export default connect(({ register }) => ({
    register
}))(RecommDatePage);
