import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Row, Col } from 'antd';
import { connect } from 'dva';
import { getQueryString } from '../../utils/utils';
import ModuleHeader from '../../components/ModuleHeader';
import './index.scss';


const BackLog = ({
    history,
    user: {
        backlogList
    },
})=>{
    
    // 取号
    const toTakeNum = ()=> {
        history.push("/takenum/list");
    }

    // 缴费
    const toPay = () => {
        history.push("/pay/list")
    }

    // 报告打印
    const toReport = () => {
        history.push("/report/list")
    }
    return (
        <div className='module-page-wrapper'>
            <ModuleHeader history={history} title={"全部待办"}/>
            <ul className='backlog-list-wrapper'>
                <li className='item-backlog' onClick={toTakeNum}>
                    <div>
                        <p className='title'>您共有<span className='blue-text'>2</span>个挂号单</p>
                    </div>
                    <div className="blue-button"><span>取号</span></div>
                </li>
                <li className='item-backlog' onClick={toPay}>
                    <div>
                        <p className='title'>您共有<span className='blue-text'>2</span>张缴费单</p>
                        <p className='title sub-title'>合计金额<span className='red-text'>60.00</span>元</p>
                    </div>
                    <div className="blue-button"><span>缴费</span></div>
                </li>
                <li className='item-backlog' onClick={toReport}>
                    <div>
                        <p className='title'>您共有<span className='blue-text'>3</span>张报告单待取</p>
                    </div>
                    <div className="blue-button"><span>打印</span></div>
                </li>
            </ul>
        </div>
    )
}


export default connect(({ user }) => ({
    user
}))(BackLog);