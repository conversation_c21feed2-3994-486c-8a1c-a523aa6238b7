
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Drawer, Row, Col, Input, } from 'antd';
import InfoModal from '../InfoModal';
import SmInput from '../SmInput';
import './index.scss';

const btnArray = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];

const NumKeyboardDrawer = ({
    visible,
    title,
    modalType,
    onClose,
    onConfirm,
}) => {

    const [dataKey, setmyKeys] = useState([]);
    const smInputRef = useRef(null);

    useEffect(()=>{
        setTimeout(()=>{
            smInputRef.current && smInputRef.current.focus();
        },0)
    },[smInputRef.current])

    const getKey = (val) => {
        setmyKeys([...dataKey, val].join(''));
        smInputRef.current && smInputRef.current.focus()
    };
    const getClean = () => {
        setmyKeys([]);
    };
    const getBackspace = () => {
        const item = [...dataKey];
        item.splice(item.length - 1, 1);
        setmyKeys(item.join(''));
        smInputRef.current && smInputRef.current.focus()
    };
    const getConfirm = () => {
        if (dataKey.length === 0 || dataKey === '') {
            InfoModal("请输入"+title)
            return;
        }
        onConfirm(dataKey)
    }
    const onListenSmCode = (code) => {
        console.log(code);
        setmyKeys(code);
    }

    return (
        <Drawer
            title={null}
            placement={"bottom"}
            closable={false}
            maskClosable={true}
            onClose={onClose}
            visible={visible}
            key={"num-keyboard"}
            height={868}
            className="number-keyboard-drawer"
            getContainer={document.getElementById("_module")}
        >

            <div className='money-title-box'>
                <h3 className="item-title">{title}</h3>
                {/* {
                    dataKey.length>0 ? 
                    <h3 className="item-title">{dataKey}</h3>:
                    <h3 className="item-title gray">{"请输入"+title}</h3>
                } */}
                <SmInput ref={smInputRef} autoFocus={true} value={dataKey} onListenSmCode={onListenSmCode} />
            </div>

            <Row className="number-keyboard-wrapper" gutter={[0, 0]}>
                <Col span={18}>
                    <Row>
                        {
                            btnArray.map((item, index) => {
                                return (
                                    <Col span={item === "0" ? 24 : 8} key={index} className={(index+1)%3===0?"border-right-none":""}>
                                        <div className='item-btn' onClick={()=>getKey(item)}>{item}</div>
                                    </Col>
                                )
                            })
                        }
                    </Row>
                </Col>
                <Col span={6}>
                    <Row>
                        <Col span={24} className="delete-col">
                            <div className='item-btn item-delete' onClick={()=>getBackspace()}>
                                <img alt="" src={require("../../assets/images/btns/delete.png")}/>
                            </div>
                        </Col>
                        <Col span={24} className="assure-col" onClick={()=>getConfirm()}>
                            <div className='item-btn item-assure'>确定</div>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </Drawer>
    )

}

export default NumKeyboardDrawer;