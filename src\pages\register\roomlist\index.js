
import React from 'react';
import moment from 'moment';
import RoomPage from '../../../components/RegRoomPage';

const RegRoomList = (props) => {

    const {
        history
    } = props;


    return (
        <RoomPage
            PAGE_NAME={"当日挂号"}
            PAGE_TYPE={"register"}
            PAGE_STEP={0}
            nextRouteName={"doctorlist"}
            history={history}
            queryParams={{
                registrationType: "0",
                registrationDate: moment().format('YYYY-MM-DD')
            }}
        />
    )
}

export default RegRoomList;