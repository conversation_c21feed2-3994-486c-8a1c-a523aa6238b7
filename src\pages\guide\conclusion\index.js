
import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { RightOutlined } from '@ant-design/icons';
import ModuleHeader from '../../../components/ModuleHeader';
import './index.scss';

const GuideConclusion = ({
    history,
    dispatch,
}) => {

    const goNext = () => {
        history.push(`/register/identity?nextpage=/register/roomlist`)  
    }


    return (
        <>
            <ModuleHeader history={history} title={"智能导诊"}/>
            <div className='guide-bg-wrapper guide-conclusion' >
                <h3 className='title'>推荐科室：神经内科</h3>
                <div className='room-wrapper'>
                    <div className='icon-box'>
                        <img width={50} height={58} src={require("../../../assets/images/icons/drgh-icon.png")} alt=""/>
                    </div>
                    <div className='content'>
                        <h2 className='content-title'>当日挂号</h2>
                        <p className='tips'>16:30以后不能挂当日号源</p>
                    </div>
                    <div className='reg-btns' onClick={()=> goNext()}>
                        <span>去挂号</span>
                        <RightOutlined />
                    </div>
                </div>
                <div className='wram-tips'>
                    <p>温馨提示：<br/>智能导诊功能仅供参考，请以医生诊断为准。</p>
                </div>
            </div>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(GuideConclusion);
