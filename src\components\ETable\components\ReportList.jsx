import React from 'react';
import { Row, Col } from 'antd';

const ReportListTable = ({
    list,
    allChecked,
    onCheck,
    onAllChecked
}) => {

    return (
        <>
            <div className='thead-row-box'>
                <Row>
                    <Col span={3}>
                        <div className='checked-box' onClick={onAllChecked}>
                            {
                                allChecked ? 
                                <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/checked.png")} />:
                                <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/no-check.png")} />
                            }
                        </div>
                    </Col>
                    <Col span={10}>
                        <div className='table-title-box title-left'>
                            <b className='title'>检验名称</b>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className='table-title-box'>
                            <b className='title'>报告日期</b>
                        </div>
                    </Col>
                    <Col span={5}>
                        <div className='table-title-box'>
                            <b className='title'>状态</b>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className='tbody-row-box'>
                {
                    list.map((item, index) => {
                        return (
                            <Row key={index} >
                                <Col span={3}>
                                    <div className='checked-box' onClick={()=>onCheck(item)}>
                                        {
                                            item.checked ?
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/checked.png")} /> :
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/no-check.png")} />
                                        }
                                    </div>
                                </Col>
                                <Col span={10}>
                                    <div className='table-title-box title-left'>
                                        <span className='title'>{item.inspectionItem}</span>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.verifyTime}</span>
                                    </div>
                                </Col>
                                <Col span={5}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.count<=0 ? "可打印":"不可打印"}</span>
                                    </div>
                                </Col>
                            </Row>
                        )
                    })
                }
            </div>
        </>
    )

}

export default ReportListTable;