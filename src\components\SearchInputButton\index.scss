.search-input-button-wrapper{
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    .input-wrapper{
        display: flex;
        align-items: center;
        width: 630px;
        height: 64px;
        background-color: #F0F6FF;
        border-radius: 10px;
        padding: 0 20px;
    }
    .ant-input-affix-wrapper-lg,.ant-input-lg{
        font-size: 28px;
    }
    .ant-input-affix-wrapper-lg{
        padding: 0 !important;
        
    }
    .ant-input-affix-wrapper{
        background: none;
        border: none;
        :focus{
            background: none;
            border: none;
            outline: none;
            box-shadow: none;
        }
        &.ant-input-affix-wrapper-focused{
            background: none;
            border: none;
            outline: none;
            box-shadow: none;
        }
        .ant-input{
            background: none;
            &::placeholder{
                color: #BBBBBB;
            }
        }
    }

    .btn{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 135px;
        height: 64px;
        border-radius: 50px;
        font-size: 30px;
        color: #FFFFFF;
        line-height: 64px;
        background-color: #1677FF;
    }
}