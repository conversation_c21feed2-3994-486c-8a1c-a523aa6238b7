.brush-face-modal{
    .ant-modal-content{
        background: none;
    }
    .ant-modal-body{
        padding: 0;
        background-color: transparent;
    }
    .modal-wrapper{
        width: 100%;
        height: 100%;
        position: relative;
        &.init{
            background: url("../../assets/images/others/zfb-brush-face-bg.png") no-repeat center;
            background-size: 100% 100%;
            .init-text{
                position: absolute;
                bottom: 100px;
                left: 0;
                right: 0;
                font-size: 42px;
                line-height: 60px;
                color: #FFFFFF;
                font-weight: 600;
                margin: 20px;
                text-align: center;
            }
            
        }
        &.faceing{
            background: url("../../assets/images/others/zfb-brush-face-bg1.png") no-repeat center;
            background-size: 100% 100%;
            .faceing-box{
                padding-top: 150px;
            }
            .avatar{
                display: block;
                width: 220px;
                height: 220px;
                margin: 0 auto 80px;
            }
            .facing-text{
                font-size: 32px;
                line-height: 46px;
                color: #FFFFFF;
                font-weight: 600;
                margin: 15px;
                text-shadow: 0 4px 3px 0 rgba(0, 0, 0, 0.46);
                text-align: center;
            }
            .loading{
                display: block;
                width: 69px;
                height: 7px;
                margin: 0 auto;
            }
        }
        .bottom-tips-box{
            position: absolute;
            bottom: 38px;
            left: 0;
            right: 0;
            text-align: center;
            >img{
                width: 208px;
                height: 54px;
            }

        }
    }
}