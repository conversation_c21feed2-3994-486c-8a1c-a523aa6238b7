import React, { useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const ConfirmModal = ({
    modalVisible,
    title,
    okText,
    cancelText,
    modalType,
    onCancel,
    onConfirm
}) => {

    return (
        <Modal 
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={false}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper confirm-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.COMFIRM_OVER_TIME} />
            <div className='modal-wrapper'>
                <img className="modal-img" src={require("../../assets/images/modal/confirm.png")} alt="提示" />
                <p className='title'>{title}</p>
                <div className='btn-wrapper'>
                    <div className='item-btn cancel'
                        onClick={() => onCancel()}
                    ><span>{cancelText}</span></div>
                    <div className='item-btn confirm'
                        onClick={onConfirm}
                    ><span>{okText}</span></div>
                </div>
            </div>
        </Modal>
    )

}

export default ConfirmModal;