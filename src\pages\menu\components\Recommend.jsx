/*
 * @Description: 
    智能推荐菜单
 */
import React from 'react';
import { Row, Col } from 'antd';

const Recommend = ({
    history
})=>{

    const toNumPage = () => {
        // 智能推荐跳转到预约挂号界面
        history.push("/recommend/date");
    }

    return <div className='recommend-wrapper'>
        <div className='left-tips'>
            <span>智能</span>
            <br/>
            <span>推荐</span>
        </div>
        <Row gutter={[20,20]}>
            <Col span={12}>
                <div className='item' onClick={()=>toNumPage()}>
                    <img src={require("../../../assets/images/others/default-doctor.png")} alt="默认医生头像" />
                    <div className='info-wrapper'>
                        <p className='department'>耳鼻咽喉科</p>
                        <p className='doctor'><span>李勇</span><span>&nbsp;&nbsp;|&nbsp;&nbsp;</span><span>五号诊室</span></p>
                        <div className='reg-btn'><span>挂号</span></div>
                    </div>
                </div>
            </Col>
            <Col span={12}>
                <div className='item' onClick={()=>toNumPage()}>
                    <img src={require("../../../assets/images/others/default-doctor.png")} alt="默认医生头像" />
                    <div className='info-wrapper'>
                        <p className='department'>内科门诊</p>
                        <p className='doctor'><span>李红</span><span>&nbsp;&nbsp;|&nbsp;&nbsp;</span><span>三号诊室</span></p>
                        <div className='reg-btn'><span>挂号</span></div>
                    </div>
                </div>
            </Col>
        </Row>
    </div>
}

export default Recommend;