
import React, {useEffect} from 'react';
import { Modal } from 'antd';
import { getVoice } from '../../services/state'
import ModalCloseItem from '../ModalCloseItem';
import QRCode from 'qrcode-react';
import './index.scss';

const codeIcons = [
    'https://pic.imgdb.cn/item/631adf4a16f2c2beb1748d7d.png',
    'https://pic.imgdb.cn/item/631954d316f2c2beb1c94187.png',
    'https://pic.imgdb.cn/item/631a9e4316f2c2beb124add5.png',
]

const PayModal = ({
    modalVisible,
    modalType,
    onCancel,
}) => {

    useEffect(()=>{
        // getVoice({speakContent: "请打开支付宝/微信扫描二维码支付"})
    },[])

    return (
        <Modal
            destroyOnClose
            visible={modalVisible}
            title={null}
            footer={null}
            maskClosable={false}
            closable={false}
            centered
            onCancel={() => onCancel()}
            width={846}
            className="public-modal-wrapper pay-modal-wrapper"
            getContainer={document.getElementById("_module")}
            bodyStyle={{
                minHeight: 648,
            }}
        >
            <ModalCloseItem num={window.config.PAY_OVER_TIME} onCancel={onCancel} />
            <h2 className='patient-text'>患者姓名：<span>张三</span>&nbsp;&nbsp;|&nbsp;&nbsp;支付金额：<span className="money">2元</span></h2>
            <div className='pay-code-wrapper'>
                <QRCode
                    size={250}
                    value={"https://qr.95516.com/03095810/unifiedNative?mchNo=101520021587&token=294f7d53522e940ea084caa354626d28d"}
                    logo={codeIcons[modalType]}
                    logoWidth={60}
                    logoHeight={60}
                />
            </div>
            <p className="pay-tips-text">请打开支付宝/微信<br />扫描二维码支付</p>
        </Modal>
    )
}

export default PayModal;