
import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Progress } from 'antd';
import InfoModal from '../../../components/InfoModal';
import ModuleHeader from '../../../components/ModuleHeader';
import ThanksModal from '../ThanksModal';
import EList from '../../../components/EList';
import gotoMenu from '../../../utils/gotoMenu';
import './index.scss';

const data = [{
    question: "您觉得住院部最需要改善的是什么？（多选）",
    id: 1,
    type: 2,
    options: [
        {
            name: "病床",
            id: "1"
        },
        {
            name: "电视机",
            id: "2"
        },
        {
            name: "卫生间",
            id: "3"
        },
        {
            name: "墙壁",
            id: "4"
        }
    ]
}, {
    question: "您对医院职员的态度是否满意？",
    id: 2,
    type: 3,
    options: [
        {
            name: "满意",
            id: "1"
        },
        {
            name: "不满意",
            id: "2"
        },
    ]
}, {
    question: "您觉得住院环境舒适干净吗？",
    id: 3,
    type: 3,
    options: [
        {
            name: "非常干净",
            id: "1"
        },
        {
            name: "干净",
            id: "2"
        },
        {
            name: "一般",
            id: "3"
        },
    ]
}]


const QuestionnaireList = ({
    user: { currentUser },
    history,
    dispatch,
}) => {
    const [list, setList] = useState([]);
    const [percent, setPercent] = useState(0);
    const [thanksVisible, setThanksVisible] = useState(false);

    useEffect(() => {
        data.forEach((item, ind) => {
            let newarr = item.options.map((el) => {
                return { ...el, checked: false }
            });
            item.options = newarr;
            item.isAnswer = false;
            item.questionIndex = ind + 1;
        })
        setList(data)
        setPercent(0)
    }, [])

    useEffect(() => {
        let arr = [...list]
        arr.forEach(item => {
            let result = item.options.some(el => el.checked)
            if (result) {
                item.isAnswer = true;
            } else {
                item.isAnswer = false;
            }
        })
        // console.log(arr)
        let answerArr = arr.filter(item => item.isAnswer);
        let num = parseFloat(answerArr.length / arr.length).toFixed(2) * 100;
        setPercent(num)
    }, [list])

    const handleOpenThanksModal = () => {
        setThanksVisible(true)
    }

    const handleCloseThanksModal = () => {
        setThanksVisible(false)
        gotoMenu(history)
    }

    const onSubmit = () => {
        let result = list.some(item => item.isAnswer)
        if (!result) {
            InfoModal("请先填写问卷");
            return
        }
        handleOpenThanksModal();
    }

    /**
     * @description: 
     * @param {*} father 问题
     * @param {*} child 选项
     * @return {*}
     */

    const onCheck = (father, child) => {
        let arr = [...list];
        arr.forEach((question) => {
            if (father.id !== question.id) { return }
            if (question.type === 3) {
                // 单选
                question.options.forEach((option) => {
                    option.checked = option.id === child.id ? true : false
                })
            } else {
                // 多选
                question.options.forEach((option) => {
                    option.checked = option.id === child.id ? !option.checked : option.checked
                })
            }
        })
        setList(arr);
    }

    return (
        <>
            <ModuleHeader history={history} title={"满意度调查"} />
            <div className='questionnaire-home-wrapper questionnaire-list-page'>
                <h4 className='title'>答题进度</h4>
                <div className='progress-wrapper'>
                    <Progress percent={percent} strokeColor="#99C4FF" strokeWidth={39} trailColor="#EBEBEB" />
                </div>
                <EList
                    data={list}
                    itemtype="QUESTIONNAIRE"
                    defaultPage={3}
                    onCheck={onCheck}
                />
                <div className='submit-btn-box' onClick={onSubmit}>
                    <span>提交</span>
                </div>
            </div>
            {thanksVisible && <ThanksModal
                modalVisible={thanksVisible}
                onCancel={handleCloseThanksModal}
            />}
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(QuestionnaireList);
