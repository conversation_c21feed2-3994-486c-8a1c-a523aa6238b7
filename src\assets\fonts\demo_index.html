<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">19.打印凭条</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">评价</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">当日挂号</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe583;</span>
                <div class="name">缴费</div>
                <div class="code-name">&amp;#xe583;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">打印 </div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">评价</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe555;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe555;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68c;</span>
                <div class="name">充值</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">充值</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe501;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe501;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">缴费</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">下载电子凭条</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">当日</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe675;</span>
                <div class="name">当日挂号</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe502;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe502;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b4;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">预约</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-dayinpingtiao"></span>
            <div class="name">
              19.打印凭条
            </div>
            <div class="code-name">.icon-dayinpingtiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingjia"></span>
            <div class="name">
              评价
            </div>
            <div class="code-name">.icon-pingjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dangriguahao"></span>
            <div class="name">
              当日挂号
            </div>
            <div class="code-name">.icon-dangriguahao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaofei"></span>
            <div class="name">
              缴费
            </div>
            <div class="code-name">.icon-jiaofei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayin"></span>
            <div class="name">
              打印 
            </div>
            <div class="code-name">.icon-dayin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuang"></span>
            <div class="name">
              评价
            </div>
            <div class="code-name">.icon-xingzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue1"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue2"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun1"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun2"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chongzhi1"></span>
            <div class="name">
              充值
            </div>
            <div class="code-name">.icon-chongzhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhengjianSVG-"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-zhengjianSVG-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayin1"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.icon-dayin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon--yuyue"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon--yuyue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chongzhi"></span>
            <div class="name">
              充值
            </div>
            <div class="code-name">.icon-chongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun3"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaofei1"></span>
            <div class="name">
              缴费
            </div>
            <div class="code-name">.icon-jiaofei1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazaidianzipingtiao"></span>
            <div class="name">
              下载电子凭条
            </div>
            <div class="code-name">.icon-xiazaidianzipingtiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dangri"></span>
            <div class="name">
              当日
            </div>
            <div class="code-name">.icon-dangri
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue3"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dangriguahao1"></span>
            <div class="name">
              当日挂号
            </div>
            <div class="code-name">.icon-dangriguahao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun4"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue4"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyue5"></span>
            <div class="name">
              预约
            </div>
            <div class="code-name">.icon-yuyue5
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayinpingtiao"></use>
                </svg>
                <div class="name">19.打印凭条</div>
                <div class="code-name">#icon-dayinpingtiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingjia"></use>
                </svg>
                <div class="name">评价</div>
                <div class="code-name">#icon-pingjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dangriguahao"></use>
                </svg>
                <div class="name">当日挂号</div>
                <div class="code-name">#icon-dangriguahao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaofei"></use>
                </svg>
                <div class="name">缴费</div>
                <div class="code-name">#icon-jiaofei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayin"></use>
                </svg>
                <div class="name">打印 </div>
                <div class="code-name">#icon-dayin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuang"></use>
                </svg>
                <div class="name">评价</div>
                <div class="code-name">#icon-xingzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue1"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue2"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun1"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun2"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chongzhi1"></use>
                </svg>
                <div class="name">充值</div>
                <div class="code-name">#icon-chongzhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhengjianSVG-"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-zhengjianSVG-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayin1"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#icon-dayin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon--yuyue"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon--yuyue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chongzhi"></use>
                </svg>
                <div class="name">充值</div>
                <div class="code-name">#icon-chongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun3"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaofei1"></use>
                </svg>
                <div class="name">缴费</div>
                <div class="code-name">#icon-jiaofei1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazaidianzipingtiao"></use>
                </svg>
                <div class="name">下载电子凭条</div>
                <div class="code-name">#icon-xiazaidianzipingtiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dangri"></use>
                </svg>
                <div class="name">当日</div>
                <div class="code-name">#icon-dangri</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue3"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dangriguahao1"></use>
                </svg>
                <div class="name">当日挂号</div>
                <div class="code-name">#icon-dangriguahao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun4"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue4"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyue5"></use>
                </svg>
                <div class="name">预约</div>
                <div class="code-name">#icon-yuyue5</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
