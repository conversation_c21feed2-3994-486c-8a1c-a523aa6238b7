
/*
 * @Description: 大号方形按钮
 */

import React from 'react';
import { Link } from 'dva/router';
import Config from '../../../config';

const SquareItem = ({
    data,
    index,
    shoNoinline,
    findUrl
}) => {
    return (
        shoNoinline ?
            <div className={`square-button-wrapper bg${index}`}>
                <div className="icon-box-1">
                    {Config.IMGS_TYPE[data.type] ?
                        <img className='menu-icon' src={require("../../../assets/images/icons/" + Config.IMGS_TYPE[data.type] + ".png")} alt="图标" /> :
                        <img className='menu-icon' src="" alt="图标" />
                    }
                </div>
                <p className='menu-text'>{data.name}</p>
                <p className='menu-tips'>{Config.TIPS_TYPE[data.type]}</p>
            </div> : 
            
            <Link to={findUrl(data)} >
                <div className={`square-button-wrapper bg${index}`}>
                    <div className="icon-box-1">
                        {Config.IMGS_TYPE[data.type] ?
                            <img className='menu-icon' src={require("../../../assets/images/icons/" + Config.IMGS_TYPE[data.type] + ".png")} alt="图标" /> :
                            <img className='menu-icon' src="" alt="图标" />
                        }
                    </div>
                    <p className='menu-text'>{data.name}</p>
                    <p className='menu-tips'>{Config.TIPS_TYPE[data.type]}</p>
                </div>
            </Link>
    )
}

export default SquareItem;