
import React from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const SuccessTipModal = (msg, closeFun) => {
	
	const Wrapp = ({
		title,
		subTitle
	}) => {
		return (
			<div className='error-info-tip-modal-wrap'>
				<img src={require("../../assets/images/modal/success.png")} alt="提示图标" />
				<p className='tip-text'>{title}</p>
				{subTitle && <p className='sub-text'>{subTitle}</p>}
			</div>
		)
		
	}
	let _modal = null
	_modal = Modal.info({
		width: 846,
		centered: true,
		closable: true,
		maskClosable: true,
		icon: <ModalCloseItem num={window.config.TIP_OVER_TIME} onCancel={()=>{
			_modal && _modal.destroy();
			_modal = null;
			if(closeFun){
				closeFun();
			}
		}}/>,
		onCancel: ()=>{
			_modal && _modal.destroy();
			_modal = null;
			if(closeFun){
				closeFun();
			}
		},
		content: <Wrapp title={msg}/>,
		getContainer: document.getElementById("_module"),

	})

	return _modal
}

export default SuccessTipModal