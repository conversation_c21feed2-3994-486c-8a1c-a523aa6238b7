.phone-keyboard-drawer {
    .ant-drawer-body {
        padding: 0 !important;
        .money-title-box {
            padding: 48px 48px 50px;
            border-radius: 16px 16px 0 0;
            .item-title {
                color: #333333;
                font-size: 40px;
                line-height: 58px;
                margin-bottom: 0;
                font-weight: 600;
                &:first-child {
                    margin-bottom: 46px;
                }
                &.gray{
                    color: #CCCCCC;
                    font-weight: 500;
                }
            }
        }

        .number-keyboard-wrapper {
            width: 100%;
            user-select: none;
            border-top: 2px solid #F5F5F5;;
            .ant-col{
                height: 150px;
                border-bottom: 2px solid #F5F5F5;
                border-right: 2px solid #F5F5F5;
                &.assure-col{
                    height: 452px;
                    background-color: #1677FF;
                }
                &.delete-col,&.assure-col{
                    border-right: none;

                }
                &:nth-child(3n){
                    border-right: none;
                }
                &:nth-child(10),&:nth-child(11),&:nth-child(12){
                    border-bottom: none;
                }
            }
            .item-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                width: 100%;
                color: #333333;
                text-align: center;
                font-weight: 500;
                font-size: 50px;
                line-height: 73px;
            }
            .item-delete{
                >img{
                    width: 68px;
                    height: 50px;
                }
            }
            .item-assure{
                color: #ffffff;
            }
        }
    }

}