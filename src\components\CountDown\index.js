/*
 * @Description: 倒计时
 */

import React, { useState, useRef, useEffect } from 'react';
import gotoMenu from '../../utils/gotoMenu';
import './index.scss';

const { OVER_TIME } = window.config;

const CountDown = (props) => {
	const { history } = props;
	const [count, setCount] = useState(OVER_TIME)
	const timeOutId = useRef(null)

	useEffect(() => {
		timeOutId.current = setTimeout(() => {
			if (count > 1) {
				setCount((c) => c - 1);
			} else {
				gotoMenu(history)
			}
		}, 1000);
		return () => clearTimeout(timeOutId.current);
	}, [count]);

	return (
		<div className="page-count-down-wrapper">
			<img alt="时钟图标" src={require("../../assets/images/btns/clock.png")} />
			<span>{count}秒</span>
		</div>
	)
}

export default CountDown