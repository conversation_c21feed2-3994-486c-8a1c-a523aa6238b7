
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import InfoModal from '../../../components/InfoModal';
import ETable from '../../../components/ETable';
import gotoMenu from '../../../utils/gotoMenu';
import RegSteps from '../../../components/Steps';
import SearchInputButton from '../../../components/SearchInputButton';

const data = [
    {
        "name": "挂号费",
        "specifications": "挂号费",
        "unit": "次",
        "price": "0",
      },
      {
        "name": "营养状况评估-营养筛查",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "10",
      },
      {
        "name": "营养咨询",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "25",
      },
      {
        "name": "病历手册",
        "specifications": "挂号费",
        "unit": "本",
        "price": "1",
      },
      {
        "name": "诊疗费",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "0",
      },
      {
        "name": "门诊诊查费",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "12",
      },
      {
        "name": "法定节假日诊查费",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "3",
      },
      {
        "name": "双休日诊查费",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "3",
      },
      {
        "name": "专家门诊挂号诊查费（副高）",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "5",
      },
      {
        "name": "专家门诊挂号诊查费（正高）",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "13",
      },
      {
        "name": "急诊挂号诊查费",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "13",
      },
      {
        "name": "门急诊留观诊查费",
        "specifications": "诊疗费",
        "unit": "日",
        "price": "12",
      },
      {
        "name": "住院诊查费",
        "specifications": "诊疗费",
        "unit": "日",
        "price": "22",
      },
      {
        "name": "院前急救费",
        "specifications": "治疗费",
        "unit": "人次",
        "price": "195",
      },
      {
        "name": "院前急救费(一般急救病人)",
        "specifications": "治疗费",
        "unit": "人次",
        "price": "160",
      },
      {
        "name": "院前急救费(小)",
        "specifications": "治疗费",
        "unit": "次",
        "price": "10",
      },
      {
        "name": "院前急救费(中)",
        "specifications": "治疗费",
        "unit": "次",
        "price": "30",
      },
      {
        "name": "体检费(体检中心)",
        "specifications": "体检费",
        "unit": "次",
        "price": "0",
      },
      {
        "name": "体检费",
        "specifications": "检查费",
        "unit": "次",
        "price": "7",
      },
      {
        "name": "资料费（体检中心）",
        "specifications": "日用品",
        "unit": "本",
        "price": "9.5",
      },
      {
        "name": "保健册",
        "specifications": "日用品",
        "unit": "本",
        "price": "4.8",
      },
      {
        "name": "胃肠道造影显像剂（500ml）",
        "specifications": "放射材料费",
        "unit": "瓶",
        "price": "95",
      },
      {
        "name": "乙肝母婴传播阻断减免",
        "specifications": "护理费",
        "unit": "人次",
        "price": "110",
      },
      {
        "name": "新型冠状病毒IgG抗体（包括总抗体检测）（免费）",
        "specifications": "上送项目",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "新型冠状病毒IgM抗体检测（免费）",
        "specifications": "上送项目",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "新型冠状病毒抗体检测IgM",
        "specifications": "上送项目",
        "unit": "次",
        "price": "20",
      },
      {
        "name": "新型冠状病毒抗体IgG（包括总抗体检测）",
        "specifications": "上送项目",
        "unit": "次",
        "price": "20",
      },
      {
        "name": "优生优育",
        "specifications": "上送项目",
        "unit": "次",
        "price": "140",
      },
      {
        "name": "新生儿疾病筛查（温岭户籍）",
        "specifications": "上送项目",
        "unit": "人",
        "price": "132",
      },
      {
        "name": "新型冠状病毒核酸混合检测",
        "specifications": "免疫",
        "unit": "每样本",
        "price": "4",
      },
      {
        "name": "新型冠状病毒RNA检测",
        "specifications": "免疫",
        "unit": "次",
        "price": "28",
      },
      {
        "name": "梅毒螺旋体特异抗体测定(自费)",
        "specifications": "免疫",
        "unit": "次",
        "price": "30",
      },
      {
        "name": "艾滋病减免",
        "specifications": "免疫",
        "unit": "次",
        "price": "15",
      },
      {
        "name": "艾梅乙减免",
        "specifications": "免疫",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "登革热NS1抗原",
        "specifications": "免疫",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "白带常规（两癌筛查）",
        "specifications": "临检",
        "unit": "次",
        "price": "6",
      },
      {
        "name": "心肌灌注断层显像",
        "specifications": "放射费",
        "unit": "次",
        "price": "638",
      },
      {
        "name": "（负荷态）心肌灌注断层显像",
        "specifications": "放射费",
        "unit": "次",
        "price": "598",
      },
      {
        "name": "内科常规检查（计生）",
        "specifications": "治疗费",
        "unit": "次",
        "price": "5",
      },
      {
        "name": "妇检（计生）",
        "specifications": "治疗费",
        "unit": "次",
        "price": "5",
      },
      {
        "name": "就诊卡",
        "specifications": "挂号费",
        "unit": "张",
        "price": "1",
      },
      {
        "name": "特需自选体检服务",
        "specifications": "诊疗费",
        "unit": "次",
        "price": "100",
      },
      {
        "name": "预防接种服务费",
        "specifications": "其它费",
        "unit": "剂次",
        "price": "28",
      },
      {
        "name": "登革热IgM",
        "specifications": "检验费",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "登革热IgG",
        "specifications": "检验费",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "雌三醇测定（产科免费）",
        "specifications": "检验费",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "唐氏综合症产前筛查（产科免费）",
        "specifications": "检验费",
        "unit": "次",
        "price": "0.01",
      },
      {
        "name": "白带常规（计生）",
        "specifications": "检验费",
        "unit": "次",
        "price": "6",
      },
      {
        "name": "心电图（计生）",
        "specifications": "检验费",
        "unit": "次",
        "price": "10",
      },
      {
        "name": "术前四项（避孕免费）",
        "specifications": "检验费",
        "unit": "次",
        "price": "60",
      },
      {
        "name": "全身麻醉（人流）",
        "specifications": "麻醉费",
        "unit": "次",
        "price": "540",
      },
      {
        "name": "救护车费(3公里以上）",
        "specifications": "其它费",
        "unit": "公里",
        "price": "2",
      },
      {
        "name": "救护车起步价(3公里内）",
        "specifications": "其它费",
        "unit": "次",
        "price": "10",
      },
      {
        "name": "病房空调费(三人以上)",
        "specifications": "空调费",
        "unit": "床/日",
        "price": "7",
      },
      {
        "name": "病房空调费(双人间)",
        "specifications": "空调费",
        "unit": "床/日",
        "price": "10",
      },
      {
        "name": "病房空调费(单人间)",
        "specifications": "空调费",
        "unit": "间/日",
        "price": "20",
      },
      {
        "name": "空调费",
        "specifications": "空调费",
        "unit": "次",
        "price": "0",
      },
      {
        "name": "急诊留观空调费",
        "specifications": "空调费",
        "unit": "日",
        "price": "7",
      },
      {
        "name": "带针可吸收性外科缝线（0/1#G11402Q)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "27",
      },
      {
        "name": "带针可吸收性外科缝线（0/3#G33243)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "32",
      },
      {
        "name": "带针可吸收性外科缝线（0/3#小针U31222)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "30",
      },
      {
        "name": "带针可吸收性外科缝线（0/2# U21402)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "30",
      },
      {
        "name": "带针可吸收性外科缝线（0/3#大针G31262Q)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "27",
      },
      {
        "name": "可吸收性外科缝线（YY090D 0# 1/2 O10*25 90CM)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "39",
      },
      {
        "name": "可吸收性外科缝线（YY390E 3-0 1/2 O6*14 90CM)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "39",
      },
      {
        "name": "可吸收性外科缝线（YY390F 3-0 1/2 O7*17 90CM)",
        "specifications": "手术材料",
        "unit": "根",
        "price": "39",
      },
      
]

const data2 = [
    {
        "name":"★(凯福隆)注射用头孢噻肟钠",
        "specifications":"2g*1瓶",
        "unit":"瓶",
        "factureInfo":"河北华民",
        "price":"52.77",
        "healthLevel":"甲"
    },
    {
        "name":"蚕砂",
        "specifications":"10g*1000g",
        "unit":"kg",
        "factureInfo":"/",
        "price":"38.75",
        "healthLevel":"甲"
    },
    {
        "name":"(太捷信)苹果酸奈诺沙星氯化钠注射液",
        "specifications":"0.5g/250ml*1袋",
        "unit":"袋",
        "factureInfo":"浙江医药股份有限公司新昌制药厂",
        "price":"84.8",
        "healthLevel":"乙"
    },
    {
        "name":"太捷信",
        "specifications":"0.5g/250ml*1袋",
        "unit":"袋",
        "factureInfo":"浙江医药股份有限公司新昌制药厂",
        "price":"84.8",
        "healthLevel":"乙"
    },
    {
        "name":"(恒托尼）硫酸特布他林雾化吸入用溶液",
        "specifications":"5mg/2ml*20支",
        "unit":"盒",
        "price":"23",
        "healthLevel":"乙"
    },
    {
        "name":"(华北)头孢克肟胶囊",
        "specifications":"100mg*20粒",
        "unit":"盒",
        "factureInfo":"华北制药河北华民药业有限责任公司",
        "price":"6.99",
        "healthLevel":"乙"
    },
    {
        "name":"盐酸氟桂利嗪片",
        "specifications":"5mg*60片",
        "unit":"盒",
        "factureInfo":"济南景笙科技有限公司委托潍坊中狮制药",
        "price":"3.78",
        "healthLevel":"甲"
    },
    {
        "name":"(普洛)琥珀酸美托洛尔缓释片",
        "specifications":"47.5mg*7片",
        "unit":"盒",
        "factureInfo":"浙江普洛康裕制药",
        "price":"4.6",
        "healthLevel":"乙"
    },
    {
        "name":"T阿贝西利片",
        "specifications":"100mg*14片",
        "unit":"盒",
        "factureInfo":"LILLY DEL CARIBE,INC",
        "price":"872.48",
        "healthLevel":"乙"
    },
    {
        "name":"(苏奇)注射用唑来膦酸浓溶液",
        "specifications":"4mg/5ml*1支",
        "unit":"支",
        "factureInfo":"扬子江药业集团四川海蓉药业",
        "price":"36",
        "healthLevel":"乙"
    },
    {
        "name":"T甲磺酸仑伐替尼胶囊",
        "specifications":"4mg*30粒",
        "unit":"盒",
        "factureInfo":"成都倍特药业有限公司",
        "price":"688",
        "healthLevel":"乙"
    },
    {
        "name":"T(健择)注射用盐酸吉西他滨",
        "specifications":"0.2g*1瓶",
        "unit":"瓶",
        "factureInfo":"希腊Vianex S.A-Plant  C",
        "price":"272.45",
        "healthLevel":"乙"
    },
    {
        "name":"健择针",
        "specifications":"0.2g*1瓶",
        "unit":"瓶",
        "factureInfo":"希腊Vianex S.A-Plant  C",
        "price":"272.45",
        "healthLevel":"乙"
    },
    {
        "name":"甲硫酸新斯的明注射液",
        "specifications":"0.5mg/1ml*1支",
        "unit":"支",
        "factureInfo":"浙江仙琚制药股份有限公司",
        "price":"35.8",
        "healthLevel":"甲"
    },
    {
        "name":"氟尿嘧啶注射液",
        "specifications":"250mg/10ml*1支",
        "unit":"支",
        "factureInfo":"天津金耀药业有限公司",
        "price":"52.5",
        "healthLevel":"甲"
    },
    {

        "name":"醋酸地塞米松片",
        "specifications":"0.75mg*100片",
        "unit":"瓶",
        "factureInfo":"天津信谊津津药业有限公司",
        "price":"9.01",
        "healthLevel":"甲"
    },
    {
        "name":"氟哌啶醇注射液",
        "specifications":"5mg/1ml*1支",
        "unit":"支",
        "factureInfo":"山东鲁抗医药集团赛特有限责任公司",
        "price":"28.7",
        "healthLevel":"甲"
    },
    {
        "name":"静注人免疫球蛋白",
        "specifications":"2.5g*1瓶",
        "unit":"瓶",
        "factureInfo":"浙江海康生物制品有限公司",
        "price":"620",
        "healthLevel":"乙"
    },
    {
        "name":"静滴人血丙球针",
        "specifications":"2.5g*1瓶",
        "unit":"瓶",
        "factureInfo":"浙江海康生物制品有限公司",
        "price":"620",
        "healthLevel":"乙"
    },
    {
        "name":"★注射用哌拉西林钠他唑巴坦钠",
        "specifications":"4.5g*1瓶",
        "unit":"瓶",
        "factureInfo":"苏州二叶药厂",
        "price":"28.97",
        "healthLevel":"乙"
    },
    {
        "name":"灭菌注射用水",
        "specifications":"500ml*1瓶",
        "unit":"瓶",
        "factureInfo":"石药银湖制药有限公司",
        "price":"5.5",
        "healthLevel":"丙"
    },
    {
        "name":"盐酸艾司洛尔注射液",
        "specifications":"0.2g/2ml*1支",
        "unit":"支",
        "factureInfo":"齐鲁制药有限公司",
        "price":"54.15",
        "healthLevel":"乙"
    },
    {
        "name":"T利鲁唑片",
        "specifications":"50mg*56片",
        "unit":"盒",
        "factureInfo":"江苏恩华",
        "price":"1160",
        "healthLevel":"乙"
    }
]
    
const QueryList = ({
    user: { currentUser },
    pay,
    history,
    dispatch,
}) => {
    const search = history.location.search;
    const queryType = search?search.split("=")[1]?search.split("=")[1]:"":"";

    const [queryList, setQueryList] = useState([...data]);

    return (
        <>
            <ModuleHeader history={history} title={"自助查询"} />
            <RegSteps current={1} type="query" />
            <div className="table-center query-list-wrapper" style={{marginTop: 22}}>
                <SearchInputButton placeholder={queryType==="1" ? "请输入药品项目简称查询" : "请输入诊疗项目简称查询"} />
                <ETable
                    tabletype={"querylist"}
                    data={queryType === "1" ? data2 : queryList}
                    defaultPage={8}
                    bgText={""}
                    buttonBoxShow={false}
                />
            </div>
        </>
    )
}


export default connect(({ user }) => ({
    user
}))(QueryList);
