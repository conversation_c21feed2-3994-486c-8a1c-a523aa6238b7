
import React, { useState, useLayoutEffect } from 'react';
import { Input } from "antd";
import { connect } from 'dva';
import InfoModal from '../../../components/InfoModal';
import SuccessTipModal from '../../../components/SuccessTipModal';
import ErrorModal from '../../../components/ErrorModal';
import ModuleHeader from '../../../components/ModuleHeader';
import PhoneKeyboardDrawer from '../../../components/PhoneKeyboardDrawer';
import gotoMenu from '../../../utils/gotoMenu';
import './index.scss';

const PatientEnfant= ({
    user: { currentUser },
    history,
}) => {
    const [phone, setPhone] = useState("");
    const [phonePickerVisible, setPhonePickerVisible] = useState(false);  //输入手机号键盘

    const toMenu = () => {
        gotoMenu(history)
    }

    // 手输手机号-抽屉
    const handleOpenPhonePickerDrawer = () => {
        setPhonePickerVisible(true);
    }

    const handleClosePhonePickerDrawer = () => {
        setPhonePickerVisible(false)
    }

    /**
     * @description: 确认输入手机号码
     * @param {*} value
     * @return {*}
     */
    const onConfirmPhone = (value) => {
        // 成功 关闭 close
        handleClosePhonePickerDrawer();
        setPhone(value)
    }

    /**
     * @description: 完成按钮
     * @return {*}
     */
    const onSubmit = () => {
        if(!phone){
            InfoModal("请先输入手机号")
            return;
        }

        // 成功
        SuccessTipModal("建档成功",toMenu);
        // 失败
        // ErrorModal("建档失败，请联系医院工作人员！",toMenu);
    }
    return (
        <div>
            <ModuleHeader history={history} title={"自助建档"} />
            <div className='patient-create-wapper'>
                <div className='item'>
                    <span className='left'>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</span>
                    <span className='right'>张三</span>
                </div>
                <div className='item'>
                    <span className='left'>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别：</span>
                    <span className='right'>男</span>
                </div>
                <div className='item'>
                    <span className='left'>生&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日：</span>
                    <span className='right'>1957-5-21</span>
                </div>
                <div className='item'>
                    <span className='left'>身份证号：</span>
                    <span className='right'>312312836181</span>
                </div>
                <div className='item input-box' onClick={handleOpenPhonePickerDrawer}>
                    <span className='left'>手&nbsp;&nbsp;机&nbsp;&nbsp;号：</span>
                    <Input type="text" readOnly placeholder="点击输入手机号" value={phone}/>
                </div>
                <div className='item'>
                    <span className='left'>家庭住址：</span>
                    <span className='right'>杭州市余杭区华一路*号</span>
                </div>
                <div className='btn' onClick={onSubmit}><span>完成</span></div>
            </div>
            {phonePickerVisible && <PhoneKeyboardDrawer
                visible={phonePickerVisible}
                title={"手机号"}
                onClose={handleClosePhonePickerDrawer}
                onConfirm={onConfirmPhone}
            />}
        </div>
    )
}

export default connect(({ user }) => ({
    user,
}))(PatientEnfant);
