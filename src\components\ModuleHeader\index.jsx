
import React from 'react';
import { LeftOutlined } from '@ant-design/icons';
import CountDown from '../CountDown';
import './index.scss';

// 返回按钮
const BackButton = ({
    goBack,
})=>{
    
    return (
        <div className='back-button-wrapper' onClick={goBack}>
            <LeftOutlined />
            <span>返回</span>
        </div>
    )

}

// 页面标题
const ModuleTitle = ({
    title
}) => {
    return (
        <div className='title-wrapper' >
            <img alt="符号图标" src={require("../../assets/images/btns/fuhao.png")}/>
            <b>{title}</b>
            <img alt="符号图标" src={require("../../assets/images/btns/fuhao.png")}/>
        </div>
    )
}

const ModuleHeader = ({
    history,
    title
})=>{
    
    return (
        <div className='module-page-header'>
            <BackButton goBack={()=>history.goBack()} />
            {title && <ModuleTitle title={title}/>}
            <CountDown history={history}/>
        </div>
    )

}

export default ModuleHeader;