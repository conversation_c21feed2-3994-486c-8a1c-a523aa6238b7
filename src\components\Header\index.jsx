import React , {useState} from 'react';
import Logo from '../../assets/images/logo/hos-logo.png';
import DateTimeText from './components/DateTime';
import EditNumberModal from '../EditNumberModal';
import InfoModal from '../InfoModal';
import 'moment/locale/zh-cn';
import './index.scss';

let waitTime = 10000;  //该时间间隔内点击才算连续点击（单位：ms)
let lastTime = 0;  //上次点击
let clickCount = 0; //连续点击次数

const Header = (props) => {

    const hashStr = window.location.hash;
    const [headerType, setHeaderType] = useState("menu");
    const [modalVisible, setModalVisible] = useState(false);

    const exitFullscreen = () => {
        if(document.exitFullscreen) {
             document.exitFullscreen();
        } else if(document.mozCancelFullScreen) {
             document.mozCancelFullScreen();
        } else if(document.webkitExitFullscreen) {
             document.webkitExitFullscreen();
        }
    }

    const onBtnClick = () => {
        var element= document.documentElement; 
        if (this.isFullscreen()) { // 全屏
            exitFullscreen();
         } else {
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullScreen) {
                element.webkitRequestFullScreen();
            } else if (element.mozRequestFullScreen) {
              element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
              element.msRequestFullscreen();  // IE11
            }
        }
    }

    // 头部多次点击
    const onClickMore = (event) => {
    
        let currentTime = new Date().getTime();
        console.log(currentTime)
        if (clickCount === 0 || (currentTime - lastTime) >= waitTime) {
            clickCount = 0;
            lastTime = new Date().getTime();
            console.log(lastTime)
        }
        console.log(clickCount)
        clickCount++;
        if (clickCount > 5) {
            clickCount = 0;
            handleOpenEditModal();
        }
    }
    

    //手动输入密码
    const handleOpenEditModal = () => {
        props.clearTimer();
        if (hashStr.indexOf("menu") > -1) {  // 首页打开
            setModalVisible(true);
        }
    }
    
    const handleCloseEditModal = () => {
        props.openTimer();
        setModalVisible(false);
    }

    const handleAssureEdit = (key) => {
        console.log(key)
        if (key === window.config.CLOSE_PROGRAME_PWD) {
            props.clearTimer();
            props.sendDeviceOrder({type: "3"}); //调用关闭浏览器程序
            setModalVisible(false);
        } else {
            setModalVisible(false);
            InfoModal("密码错误");
        }
    } 



    return (
        <div className="header-wrapper">
            <div className="logo-box">
                <img src={Logo} alt="" />
            </div>
            <div className="right" onClick={onClickMore} >
                <DateTimeText/>
            </div>
            {
                modalVisible &&  <EditNumberModal
                    visible={modalVisible}
                    titleText={"请输入退出密码"}
                    onCancel={handleCloseEditModal}
                    onConfirm={handleAssureEdit}
                    MODAL_TYPE={"OFF"}
                />
            }
        </div>
    )
}

export default Header;