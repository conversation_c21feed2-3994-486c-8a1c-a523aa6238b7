.number-wrapper{
    width: 768px;
    margin: 24px auto 0;   
    .time-title{
        font-size: 32px;
        color: #333333;
        line-height: 46px;
        margin-bottom: 15px;
    }

    .time-wrapper{
        display: grid;
        justify-content: center;
        grid-template-columns: repeat(auto-fill, 136px);
        grid-gap: 20px;
        width: 100%;
        list-style: none;
        padding: 0;
        margin-bottom: 32px;
        .time-item{
            width: 136px;
            height: 80px;
            color: #333333;
            font-size: 32px;
            line-height: 80px;
            background-color: #E8F5FF;
            border-radius: 16px;
            text-align: center;
            transition: 0.2s all;
            &.zh{
                background-color: #EDEDED;
            }
            &.actived{
                transform: scale(0.98);
                background-color: #1677FF;
                color: #FFFFFF;
            }
            &:active{
                transform: scale(0.98);
                background-color: #1677FF;
                color: #FFFFFF;
            }
        }
    }
    
    .range-wrapper{
        display: grid;
        justify-content: center;
        grid-template-columns: repeat(auto-fill, 240px);
        grid-gap: 24px;
        width: 100%;
        list-style: none;
        padding: 0;
        margin-bottom: 30px;
        .range-item{
            width: 240px;
            height: 130px;
            color: #333333;
            font-size: 32px;
            line-height: 130px;
            background-color: #E8F5FF;
            border-radius: 16px;
            text-align: center;
            &:active{
                background-color: #1677FF;
                color: #FFFFFF;
            }
        }
    }
}