/*
 * @Description:
    刷脸登录
 */
import React, { useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import FaceModalCloseItem from '../FaceModalCloseItem';
import './index.scss';

const BrushFaceModal = ({
    modalVisible,
    modalType,
    onCancel,
}) => {
    const [faceType, setFaceType] = useState("init");

    useEffect(()=>{
        setTimeout(()=>{
            setFaceType("faceing")
        },2000)
    },[])

    return (
        <Modal
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={true}
            closable={false}
            width={504}
            centered
            className="public-modal-wrapper brush-face-modal"
            getContainer={document.getElementById("_module")}
            bodyStyle={{
                height: 806,
            }}
        >
            <div className={faceType==="init" ? "modal-wrapper init": "modal-wrapper faceing"}>
                <FaceModalCloseItem onCancel={onCancel} num={window.config.BRUSH_FACE_OVER_TIME} />
                {
                    faceType === "init" ?
                    <h3 className='init-text'>刷脸启动中...</h3>:
                    <div className='faceing-box'>
                        <img className='avatar' src={require("../../assets/images/others/zfb-brush-face-avatar.png")} alt=""/>
                        <h4 className='facing-text'>正在识别</h4>
                        <img className='loading' src={require("../../assets/images/others/Loading.png")} alt=""/>
                    </div>
                }
                <div className='bottom-tips-box'>
                    <img src={require("../../assets/images/others/zfb-safe-text.png")} alt="" />
                </div>
            </div>
        </Modal>
    )

}

export default BrushFaceModal;