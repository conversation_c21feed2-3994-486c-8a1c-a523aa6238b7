/*
 * @Description: 搜索框带按钮
 */

import React, {useState} from 'react';
import { Input } from 'antd';
import NormalKeyborad from '../NormalKeyboard';
import './index.scss';

const SearchInputButton = ({
    placeholder,
    onSearch,
})=>{

    const [value, setValue] = useState('')
    const [normalKeyboardVisible, setNormalKeyBoardVisible] = useState(false)
    
    const SearchIcon = () => {
        return <img width="40" height="40" src={require("../../assets/images/btns/search2.png")} alt="搜索图标蓝色"/>
    }
    
    const handleCloseKeyModal = () => {
        setNormalKeyBoardVisible(false)
    }

    const onListenKeyInput = (val) => {
        setValue(val?.toUpperCase());
    }

    return (
        <>
            <div>
                <div className="search-input-button-wrapper">
                    <div className='input-wrapper'>
                        <Input
                            style={{ width: "100%" }}
                            placeholder={placeholder}
                            prefix={ <SearchIcon /> }
                            size="large"
                            onFocus={()=>{setNormalKeyBoardVisible(true)}}
                            value={value}
                        />
                    </div>
                    <div className='btn' onClick={()=>{
                        onSearch(value)
                    }}><span>查 找</span></div>
                </div>
            </div>
            { normalKeyboardVisible && <NormalKeyborad
                value={value}
                visible={normalKeyboardVisible}
                onCancel={handleCloseKeyModal}
                onListenKeyInput={onListenKeyInput}
            />}
        </>
    )

}

export default SearchInputButton;