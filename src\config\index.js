const menuData =  [
    {
        type: 'register',
        menu_link: '/register/identity?nextpage=/register/roomlist',
        name: '当日挂号',
        steps: [
            '选择科室', '选择医生', 
        ]
    }, 
    {
        type: 'takenum',
        menu_link: '/takenum/identity?nextpage=/takenum/list',
        name: '预约取号',
        steps: []
    },
    {
        type: 'reserve',
        menu_link: '/reserve/identity?nextpage=/reserve/date',
        name: '预约挂号',
        steps: [
            '选择日期', '选择科室', '选择医生',
        ]
    }, 
    {
        type: 'recommend',
        menu_link: '/recommend/identity?nextpage=/recommend/date',
        name: '智能推荐',
        steps: [ '选择日期', '选择时段']
    },
    {
        type: 'pay',
        menu_link: '/pay/identity?nextpage=/pay/list',
        name: '自助缴费',
        steps: [
            '缴费订单', '缴费明细', '支付方式', '缴费成功'
        ]
    }, 
    {
        type: 'nuclein',
        menu_link: '/hskd/identity?nextpage=/hskd/list',
        name: '自助开单',
        steps: [
            '选择开单', '选择支付' 
        ]
    }, 
    {
        type: 'patient',
        menu_link: '/patient/mold',
        name: '自助建档',
        steps: []
    },
    {
        type: 'query',
        // menu_link: '/query/identity?nextpage=/query/mold',
        menu_link: '/query/mold',
        name: '自助查询',
        steps: [
            '选择类型', '查询信息'
        ]
    }, 
    {
        type: 'ticket',
        menu_link: '/ticket/identity?nextpage=/ticket/mold',
        name: '凭条补打',
        steps: [
            '选择类型', '选择凭条'
        ]
    },

    {
        type: 'questionnaire',
        menu_link: '/questionnaire/identity?nextpage=/questionnaire/home',
        name: '满意度调查',
        steps: []
    },
    {
        type: 'guide',
        menu_link: '/guide/home',
        name: '智能导诊',
        steps: []
    },
    {
        type: 'report',
        menu_link: '/report/identity?nextpage=/report/list',
        name: '报告单打印',
        steps: []
    }
]

const IMGS_TYPE = {
    "register": "drgh-icon",
    "reserve": "yygh-icon",
    "pay": "zzjf-icon",
    "takenum": "yyqh-icon",
    "nuclein": "hskd-icon",
    "patient": "zzjd-icon",
    "report":  "bgdy-icon",
}

const IMGS_TYPE2 = {
    "query": {
        "url": "zzcx-stoke",
        "width": 56,
        "height": 56,
    },
    "ticket": {
        "url": "ptbd-stoke",
        "width": 56,
        "height": 64,
    },
    "guide": {
        "url": "zndz-stoke",
        "width": 56,
        "height": 60,
    },
    "questionnaire": {
        "url": "myddc-stoke",
        "width": 56,
        "height": 57,
    },
    "more": {
        "url": "more-stoke",
        "width": 56,
        "height": 55,
    },
    "report": {
        "url": "bgdy-stoke",
        "width": 56,
        "height": 58,
    },
    "nuclein": {
        "url": "hskd-stoke",
        "width": 56,
        "height": 58,
    }
}

const TIPS_TYPE = {
    "register": "16：30以后不能挂当日号源",
    "takenum": "可取号或取消预约",
    "reserve": "可预约未来一周的号源",
    "pay": "可使用多种支付方式进行缴费",
    "nuclein": "选择项目自助开单",
    "patient": "建档后更方便挂号、缴费、查询",
    "report": "选择检查、检验报告打印",
}

export default {
    menuData,
    IMGS_TYPE,
    IMGS_TYPE2,
    TIPS_TYPE
}

