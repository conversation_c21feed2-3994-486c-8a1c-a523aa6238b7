import React from 'react';
import { Router, Route, Switch, Redirect} from 'dva/router';
import { ConfigProvider } from 'antd';
import zh_CN from 'antd/lib/locale-provider/zh_CN';

import Empty from '../components/Empty';
import App from '../App';
import Home from '../pages/home';
import Identity from '../pages/identity';
import Menu from '../pages/menu';   //主页菜单
import RecomMenu from '../pages/menu/recomMenu';    //智能推荐菜单
import More from '../pages/more';
import BackLog from '../pages/backlog';   //全部代办
import Password from '../pages/password'; // 密码输入

// 挂号
import Register from '../pages/register';
import RegRoomList from '../pages/register/roomlist';
import RegDoctorList from '../pages/register/doctorlist';
import RegNumberList from '../pages/register/numberlist';
import RegConfirm from '../pages/register/confirm';

// 预约挂号
import Reserve from '../pages/reserve';
import ResDateList from '../pages/reserve/date';
import ResRoomList from '../pages/reserve/roomlist';
import ResDoctorList from '../pages/reserve/doctorlist';
import ResNumberList from '../pages/reserve/numberlist';
import ResConfirm from '../pages/reserve/confirm';

// 智能推荐科室医生
import Recommend from '../pages/recommend';
import RecommDateList from '../pages/recommend/date';
import RecommNumberList from '../pages/recommend/numberlist';
import RecommConfirm from '../pages/recommend/confirm';

// 预约取号
import TakeNum from '../pages/takenum';
import TakeNumList from '../pages/takenum/list';
import TakeNumConfirm from '../pages/takenum/confirm';

// 自助缴费
import Pay from '../pages/pay';
import PayList from '../pages/pay/list';
import PayDeatils from '../pages/pay/details';
import PayConfirm from '../pages/pay/confirm';

// 报告单打印
import Report from '../pages/report';
import ReportMold from '../pages/report/mold';
import ReportList from '../pages/report/list';

// 核酸开单
import Nuclein from '../pages/nuclein';
import NucleinList from '../pages/nuclein/list';
import NucleinConfirm from '../pages/nuclein/confirm';

// 凭条补打
import Ticket from '../pages/ticket';
import TicketMold from '../pages/ticket/mold';
import TicketList from '../pages/ticket/list';

// 自助查询
import Query from '../pages/query';
import QueryMold from '../pages/query/mold';
import QueryList from '../pages/query/list';

// 自助建档
import Patient from '../pages/patient';
import PatientMold from '../pages/patient/mold';
import PatientAdult from '../pages/patient/create';
import PatientEnfant from '../pages/patient/Enfant';

// 满意度调查
import Questionnaire from '../pages/questionnaire';
import QuestionnaireHome from '../pages/questionnaire/home';
import QuestionnaireList from '../pages/questionnaire/list';

// 智能导诊
import Guide from '../pages/guide';
import GuideHome from '../pages/guide/home';
import GuideConclusion from '../pages/guide/conclusion';


const RouterConfig = (({ history }) => (
	<Router history={history}>
        <ConfigProvider locale={zh_CN} renderEmpty={Empty}>
            <App>
                <Route path="/" render={() =>
                    <Home>
                        <Switch>
                            <Route path="/identity" component={Identity}/>
                            <Route path="/menu" component={Menu}/>
                            <Route path="/recommenu" component={RecomMenu}/>
                            <Route path="/more" component={More}/>
                            <Route path="/password" component={Password} />
                            <Route path="/backlog" component={BackLog}/>
                            <Route path="/register" render={() =>
                                <Register>
                                    <Switch>
                                        <Route path="/register/identity" component={Identity} />
                                        <Route path="/register/roomlist" component={RegRoomList}/>
                                        <Route path="/register/doctorlist" component={RegDoctorList}/>
                                        <Route path="/register/numberlist" component={RegNumberList}/>
                                        <Route path="/register/confirm" component={RegConfirm}/>
                                    </Switch>
                                </Register>
                            }/>
                            <Route path="/reserve" render={() =>
                                <Reserve>
                                    <Switch>
                                        <Route path="/reserve/identity" component={Identity} />
                                        <Route path="/reserve/date" component={ResDateList}/>
                                        <Route path="/reserve/roomlist" component={ResRoomList}/>
                                        <Route path="/reserve/doctorlist" component={ResDoctorList}/>
                                        <Route path="/reserve/numberlist" component={ResNumberList}/>
                                        <Route path="/reserve/confirm" component={ResConfirm}/>
                                    </Switch>
                                </Reserve>
                            }/>
                            <Route path="/recommend" render={() =>
                                <Recommend>
                                    <Switch>
                                        <Route path="/recommend/identity" component={Identity} />
                                        <Route path="/recommend/date" component={RecommDateList}/>
                                        <Route path="/recommend/numberlist" component={RecommNumberList}/>
                                        <Route path="/recommend/confirm" component={RecommConfirm}/>
                                    </Switch>
                                </Recommend>
                            }/>
                            <Route path="/takenum" render={() =>
                                <TakeNum>
                                    <Switch>
                                        <Route path="/takenum/identity" component={Identity}/>
                                        <Route path="/takenum/list" component={TakeNumList}/>
                                        <Route path="/takenum/confirm" component={TakeNumConfirm}/>
                                    </Switch>
                                </TakeNum>
                            }/>
                            <Route path="/pay" render={() =>
                                <Pay>
                                    <Switch>
                                        <Route path="/pay/identity" component={Identity} />
                                        <Route path="/pay/list" component={PayList} />
                                        <Route path="/pay/details" component={PayDeatils} />
                                        <Route path="/pay/confirm" component={PayConfirm} />
                                    </Switch>
                                </Pay>
                            } />
                            <Route path="/report" render={() =>
                                <Report>
                                    <Switch>
                                        <Route path="/report/identity" component={Identity} />
                                        <Route path="/report/mold" component={ReportMold} />
                                        <Route path="/report/list" component={ReportList} />
                                    </Switch>
                                </Report>
                            } />
                            <Route path="/hskd" render={() =>
                                <Nuclein>
                                    <Switch>
                                        <Route path="/hskd/identity" component={Identity} />
                                        <Route path="/hskd/list" component={NucleinList} />
                                        <Route path="/hskd/confirm" component={NucleinConfirm} />
                                    </Switch>
                                </Nuclein>
                            } />
                            <Route path="/ticket" render={() =>
                                <Ticket>
                                    <Switch>
                                        <Route path="/ticket/identity" component={Identity} />
                                        <Route path="/ticket/mold" component={TicketMold} />
                                        <Route path="/ticket/list" component={TicketList} />
                                    </Switch>
                                </Ticket>
                            } />
                            <Route path="/query" render={() =>
                                <Query>
                                    <Switch>
                                        <Route path="/query/identity" component={Identity} />
                                        <Route path="/query/mold" component={QueryMold} />
                                        <Route path="/query/list" component={QueryList} />
                                    </Switch>
                                </Query>
                            } />
                            <Route path="/patient" render={() =>
                                <Patient>
                                    <Switch>
                                        <Route path="/patient/identity" component={Identity} />
                                        <Route path="/patient/mold" component={PatientMold} />
                                        <Route path="/patient/create" component={PatientAdult} />
                                        <Route path="/patient/enfant" component={PatientEnfant} />
                                    </Switch>
                                </Patient>
                            } />
                            <Route path="/questionnaire" render={() =>
                                <Questionnaire>
                                    <Switch>
                                        <Route path="/questionnaire/identity" component={Identity} />
                                        <Route path="/questionnaire/home" component={QuestionnaireHome} />
                                        <Route path="/questionnaire/list" component={QuestionnaireList} />
                                    </Switch>
                                </Questionnaire>
                            } />
                            <Route path="/guide" render={() =>
                                <Guide>
                                    <Switch>
                                        <Route path="/guide/home" component={GuideHome} />
                                        <Route path="/guide/conclusion" component={GuideConclusion} />
                                    </Switch>
                                </Guide>
                            } />
                            <Redirect to="/menu"/>
                        </Switch>
                    </Home>
                }/>
            </App>
        </ConfigProvider>
	</Router>
));

export default RouterConfig;
