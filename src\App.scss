@import '~antd/dist/antd.css';

.table-center{
    width: 790px;
    margin: 0 auto;
}



// 公共弹窗样式
.ant-modal-mask{
    position: absolute !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
}
.ant-modal-wrap{
    position: absolute !important;
}
.public-modal-wrapper{
    .ant-modal-content{
        border-radius: 32px;
    }
    .ant-modal-body{
        // padding: 63px;
        position: relative;
    }

}
.ant-modal-confirm{
    .ant-modal-content{
        border-radius: 32px;
        .ant-modal-close{
            display: none !important;
        }

        .ant-modal-body{
            min-height: 648px;
            padding: 24px !important;
        }

        .ant-modal-confirm-btns{
            display: none !important;
        }

        .error-info-tip-modal-wrap{
            padding-top: 125px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            >img{
              width: 160px;
              height: 160px;
              margin-bottom: 50px;
            }
            .tip-text{
              font-size: 42px;
              color: #333333;
              line-height: 60px;
              font-weight: 600;
            }
            .sub-text{
                font-size: 42px;
                color: #FF8D1A;
                line-height: 60px;
                font-weight: 600;
                margin-top: 50px;
            }
        }
    }
}

.module-page-wrapper{
    padding: 30px 32px;
}

.scroll-style{
    overflow-y: auto;
    overflow-x: hidden;
}
/* 滚动条整体样式(高宽分别对应横竖滚动条的尺寸) */
.scroll-style::-webkit-scrollbar {
    width: 28px;
    height: 0;
}

/* 滚动条里面小方块 */
.scroll-style::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: #D0E4FF;
    -webkit-box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

/* 滚动条里面轨道 */
.scroll-style::-webkit-scrollbar-track {
    border-radius: 6px;
    background-color: #F7F7F7;
}

// drawer 底部抽屉函数样式封装
.others{
    .ant-drawer{
        position: static;
    }
    .ant-drawer-bottom .ant-drawer-mask{
        border-radius: 30px 30px 0 0;
        background-color: rgba(0, 0, 0, 0.8) !important;
    }
    .ant-drawer-bottom .ant-drawer-content-wrapper{
        .ant-drawer-content{
            border-radius: 16px 16px 0 0;
        }
    }
}

.identity{
    .ant-drawer{
        position: static;
    }
    .ant-drawer-bottom .ant-drawer-mask{
        border-radius: 30px 30px 0 0;
        background-color: rgba(0, 0, 0, 0.8) !important;
    }
    .ant-drawer-bottom .ant-drawer-content-wrapper{
        .ant-drawer-content{
            border-radius: 16px 16px 0 0;
        }
    }
}

// 确认单公共样式
.confirm-page-wrapper {
    margin-top: 56px;
    .confirm-info-list-wrapper {
        position: relative;
        width: 100%;
        padding: 88px 30px 44px;
        background-color: #F0F6FF;
        border-radius: 22px;
        border: 1px solid #99C4FF;
        .red-text{
            color: #FF6116;
        }
        .circle-box {
            position: absolute;
            top: 195px;
            width: 28px;
            height: 28px;
            background-color: #ffffff;
            border: 1px solid #99C4FF;
            border-radius: 50%;
            &.left {
                left: -14px;
                clip: rect(0 28px 28px 13px);  //图形裁剪
            }
    
            &.right {
                right: -14px;
                clip: rect(0 16px 28px 0);   //图形裁剪
            }
        }
    
        .info-bold-box {
            padding: 0 2px 10px;
            border-bottom: 2px dashed #84B5FF;
            margin-bottom: 23px;
            
        }
    
        .info-bold {
            color: #333333;
            font-size: 28px;
            line-height: 40px;
            font-weight: 600;
            margin-bottom: 15px;
        }
    
        .item-message {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2px;
            
            >span {
                font-size: 28px;
                line-height: 41px;
                color: #666666;
                font-weight: 500;
                min-width: max-content;
                &.black-span {
                    color: #333333;
                }
            }
        }
        
        .info-button{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 280px;
            height: 100px;
            margin: 48px auto 0;
            text-align: center;
            color: #ffffff;
            border-radius: 50px;
            background-color: #1677FF;
            font-size: 42px;
        }
        &.col12{
            height: 736px;
            
        }
        &.col24{
            width: 790px;
            margin: 0 auto;
                padding: 88px 38px 44px;
                .circle-box {
                    top: 136px;
                }
                .info-bold-box{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-left: 42px;
                    padding-right: 42px;
                }
                .item-message{
                    padding-left: 42px;
                    padding-right: 42px;
                }
            
        }
    
    }
}

// 选择类型公共样式
.mold-wrapper{
    width: 100%;
    padding: 0;
    margin: 96px 0 0;
    list-style: none;
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(auto-fill, 320px);
    grid-gap: 0 46px;
    .item{
        display: flex;
        align-items: center;
        flex-direction: column;
        overflow: hidden;
        width: 320px;
        height: 360px;
        border-radius: 16px;
        cursor: pointer;
        .icon-box{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 140px;
            height: 140px;
            margin-top: 44px;
            margin-bottom: 32px;
        }
        .title{
            text-align: center;
            font-size: 60px;
            line-height: 74px;
            color: #333333;
            font-weight: 600;
            margin-bottom: 0;
        }
        &.bggreen{
            background-color: #E9FFFD;
            .icon-box{
                background: url("./assets/images/icons/green-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }
        &.bgblue{
            background-color: #E8F5FF;
            .icon-box{
                background: url("./assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
                background-size: 100% 100%;
            }
        }
    }
    &.create{
        .item .icon-box{
            width: 180px;
            height: 180px;
            margin-top: 23px;
            margin-bottom: 40px;
        }
    }
    &.query{
        .item .icon-box{
            margin-top: 35px;
            margin-bottom: 20px;
        }
        .item .title{
            font-size: 50px;
            line-height: 66px;
        }
    }

    &.report{
        .item{

        }
        .icon-box{
            width: 160px;
            height: 244px;
            margin-top: 28px;
            margin-bottom: 0;
            img{
                width: 80px;
                height: 80px;
            }
        }
        .title{
            margin-top: 20px;
            padding-bottom: 20px;
            font-size: 50px;
            line-height: 66px;
            }
        }
}