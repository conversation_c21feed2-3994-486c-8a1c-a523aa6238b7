
import { getUserInfo, createUser } from '../services/user';

const Model = {
	namespace: 'user',
	state: {
		currentUser: {},
		cardType: '', //实体卡类型
		backlogList: [
			// {  //模拟数据
			// 	type: 1, //取号
			// 	task: 2,
			// },{
			// 	type: 2, //缴费
			// 	task: 1,
			// 	totalMoney: "60.00",
			// },{
			// 	type: 3, //报告单打印
			// 	task: 2,
			// }
		]
	},
	effects: {
		*fetchUser({ payload, callback }, { call, put }) {
			const response = yield call(getUserInfo, payload);
			yield put({
				type: 'queryUser',
				payload: response.data ? response.data : {},
			});
			if (callback && typeof callback === 'function') {
				callback(response); // 返回结果
			}
		},
		*createUser({ payload, callback }, { call, put }) {
			// console.log(payload)
			const response = yield call(createUser, payload);
			yield put({
				type: 'queryUser',
				payload: response.data ? response.data : payload,
			});
			if (callback && typeof callback === 'function') {
				callback(response.data); // 返回结果
			}
		},
	},
	reducers: {
		queryUser(state, action) {
			return { ...state, currentUser: action.payload };
		},
		queryUserState(state, action) {
			return { ...state, ...action.payload };
		}
	},
	subscriptions: {
		setup({ dispatch, history }) {
			history.listen(({ pathname }) => {
				if (pathname.indexOf('menu') > -1 || pathname.indexOf('identity') > -1) {
					// console.log(pathname)
					dispatch({
						type: 'queryUser',
						payload: {}
					})
				}
			})
		}
	},
};

export default Model;