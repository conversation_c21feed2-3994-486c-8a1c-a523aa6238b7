import React from 'react';
import { Row, Col } from 'antd';

const PayListTable = ({
    list,
    onCheck,
    toDetails
}) => {

    return (
        <>
            <div className='thead-row-box'>
                <Row>
                    <Col span={2}></Col>
                    <Col span={5}>
                        <div className='table-title-box'>
                            <b className='title'>科室</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>医生</b>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className='table-title-box'>
                            <b className='title'>开单日期</b>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className='table-title-box'>
                            <b className='title'>费用</b>
                        </div>
                    </Col>
                    <Col span={3}>
                        <div className='table-title-box'>
                            <b className='title'>操作</b>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className='tbody-row-box'>
                {
                    list.map((item, index) => {
                        return (
                            <Row key={index} >
                                <Col span={2}>
                                    <div className='checked-box' onClick={()=>onCheck(item)}>
                                        {
                                            item.checked ?
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/checked.png")} /> :
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/no-check.png")} />
                                        }
                                    </div>
                                </Col>
                                <Col span={5}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.billingDepartment}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.billingDoctor}</span>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.billTime}</span>
                                    </div>
                                </Col>
                                <Col span={4}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.billCost}</span>
                                    </div>
                                </Col>
                                <Col span={3} onClick={()=>toDetails(item)}>
                                    <div className="opt-btn">详情</div>
                                </Col>
                            </Row>
                        )
                    })
                }
            </div>
        </>
    )

}

export default PayListTable;