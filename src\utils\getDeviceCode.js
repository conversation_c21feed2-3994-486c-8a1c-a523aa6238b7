
// 获取设备号
const getDeviceCode = () =>{
    function getQueryValue(queryName) {
        const searchStr = decodeURI(window.location.hash && window.location.hash.split("?")[1]);
        const query = decodeURI(searchStr);
        // debugger
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split("=");
            if (pair[0] == queryName) { 
                return pair[1]; 
            }
        }
        return null;
    }
    
    function getCode(queryName){
        if(!queryName) return;
        let str = getQueryValue(queryName);
        let urlStr = localStorage.getItem(queryName);
        // console.log("地址栏的deviceCode值" + str);
        // console.log("LOCALSTORAGE的deviceCode值" + str);   
        if(str){
            localStorage.setItem("deviceCode", str);
            if(str!==urlStr){
                localStorage.removeItem("deviceCode");
                localStorage.setItem("deviceCode", str);
            }
            return str;
        }else{
            return urlStr;
        }
    }
    window.config.DEVICE_CODE = getCode("deviceCode");
    window.config.ZDBH = getCode("deviceCode");
}

getDeviceCode();