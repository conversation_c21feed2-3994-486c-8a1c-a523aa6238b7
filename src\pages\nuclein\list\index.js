
import React, { useState } from 'react';
import { connect } from 'dva';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';
import './index.scss';

const data = [
    {
        key: '1',
        billingDepartment: '(自费)胸部平扫和心电图',
        billNo: 23,
        billCost: '20.00',
        billExplain: "(自费)胸部平扫和心电图",
    },
    {
        key: '2',
        billingDepartment: '(自费)血常规(一套)',
        billNo: 24,
        billCost: '16.00',
        billExplain: "(自费)血常规(一套)",
    }
]

const NucleinList = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const [list, setList] = useState([...data])


    const goNext = (item) => {
        
        dispatch({
            type: "pay/setSelectNuclein",
            payload: {...item}
        })
        
        history.push("/hskd/confirm")
    }


    return (
        <>
            <ModuleHeader history={history} title={"自助开单"}/>
            <RegSteps current={0} type={"nuclein"} />
            <ul className='table-center nuclein-list-wrapper'>
                {
                    list.map((item,index)=>{
                        return (
                            <li className='nuclein-item' key={index} onClick={()=>goNext(item)}>
                                <div className='info-box'>
                                    <p>价格(元)：<span className='money'>{item.billCost}元</span></p>
                                    <p>项目名称: </p>
                                    <p><b>{item.billExplain}</b></p>
                                </div>
                                <div className='nuclein-btn'>
                                    <span>开单</span>
                                </div>
                            </li>

                        )
                    })
                }
            </ul>
        </>
    )
}


export default connect(({ user }) => ({
    user
}))(NucleinList);
