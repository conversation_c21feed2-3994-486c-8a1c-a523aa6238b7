
import React, { useState, useLayoutEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import ModuleHeader from '../../../components/ModuleHeader';
import RegSteps from '../../../components/Steps';

const QueryMold = ({
    user: { currentUser },
    history,
    dispatch,
}) => {

    const toList = (type) => {
        history.push(`/query/list?type=${type}`)
    }

    return (
        <>
            <ModuleHeader history={history} title={"自助查询"}/>
            <RegSteps current={0} type="query" />
            <ul className="mold-wrapper query">
                <li className="item bggreen" onClick={()=>toList("1")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/yp-icon.png")} alt="药品查询图标"/>
                    </div>
                    <p className="title">药品<br/>价格查询</p>
                </li>
                <li className="item bgblue" onClick={()=>toList("2")}>
                    <div className='icon-box'>
                        <img src={require("../../../assets/images/icons/zl-icon.png")} alt="诊疗查询图标"/>
                    </div>
                    <p className="title">诊疗<br/>价格查询</p>
                </li>
            </ul>
        </>
    )
}

export default connect(({ user }) => ({
    user,
}))(QueryMold);
