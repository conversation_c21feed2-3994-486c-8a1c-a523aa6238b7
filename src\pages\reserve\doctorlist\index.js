import React from 'react';
import moment from 'moment';
import DoctorPage from '../../../components/RegDoctorPage';

const ResDoctorList = (props) => {

    const {
        history
    } = props;


    return (
        <DoctorPage
            history={history}
            PAGE_NAME={"预约挂号"}
            PAGE_STEP={2}
            PAGE_TYPE={"reserve"}
            queryParams={{
                beginTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            }}
            nextRouteName="numberlist"
        />
    )
}

export default ResDoctorList;