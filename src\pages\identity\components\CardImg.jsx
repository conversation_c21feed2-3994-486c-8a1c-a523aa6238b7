import React from "react";
import idCard from "../../../assets/images/cards/id-card.png";
import socialCard from "../../../assets/images/cards/social-card.png";
import elMedCard from '../../../assets/images/cards/el-med-card.png';
import input from "../../../assets/images/cards/input-patient-id.png";
import elHealthCard from "../../../assets/images/cards/el-health-card.png";
import socialCardzh from "../../../assets/images/cards/social-card-zh.png";
import elMedCardzh from '../../../assets/images/cards/el-med-card-zh.png';
import inputPatientId from '../../../assets/images/cards/input-patient-id.png';
import face from '../../../assets/images/cards/face.png'
import socialFace from '../../../assets/images/cards/social-face.png'
import jzkCard from "../../../assets/images/cards/jzk.png";
import foreignerCard from "../../../assets/images/cards/foreigner-card.png";
import scanCode from "../../../assets/images/cards/scan-code.png"

import "../index.scss";

const CARD_IMG = {
    elMedCard,
    idCard,
    socialCard,
    input,
    elHealthCard,
    socialCardzh,
    elMedCardzh,
    inputPatientId,
    face,
    socialFace,
    jzkCard,
    foreignerCard,
    scanCode
}

const CardImg = ({type})=>{
    return <img src={CARD_IMG[type]} className="card-img" alt=""/>
}

export default CardImg;