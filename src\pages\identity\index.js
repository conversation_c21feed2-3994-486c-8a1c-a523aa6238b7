/*
 * @Description: 核身界面
 */
import React, { useState, useEffect, useRef } from 'react';
import { Row, Col } from 'antd';
import { getQueryString } from '../../utils/utils';
import { connect } from 'dva';
import CardBox from './components/CardBox';
import TypeModal from '../../components/TypeModal';
import BrushFaceModal from '../../components/BrushFaceModal'; 
import WarmTipsTitle from '../../components/WarmTipsTitle';
import ModuleHeader from '../../components/ModuleHeader';
import NumKeyboardDrawer from '../../components/NumKeyboardDrawer';
import './index.scss';

const Identity = ({
    user: { backlogList },
    dispatch,
    history,
    location,
}) => {
    const query = location.search
    const nextpage = getQueryString(query, 'nextpage');

    const [typeModalVisible, setTypeModalVisible] = useState(false);
    const [faceModalVisible, setFaceModalVisible] = useState(false);
    const [numberKeyboardVisible, setNumberKeyboardVisible] = useState(false);
    const [modalType, setModalType] = useState();

    const timer = useRef(null);

    useEffect(()=>{
        timer.current && clearTimeout(timer.current)
    },[])

     // 刷脸提示弹窗
     const handleOpenFaceModal = () => {
        setFaceModalVisible(true)
        timer.current = setTimeout(()=>{
            if(nextpage === '/pay/list' ){
                history.push("/backlog")
            }else{
                history.push(nextpage);
            }
        },5000)
    }

    const handleCloseFaceModal = () => {
        setFaceModalVisible(false)
    }

    const handleCloseKeyModal = () => {
        setNumberKeyboardVisible(false)
    }

    // 核身提示弹窗  有待办跳转到待办界面，没有待办跳转到智能推荐界面，点击回到主页跳转到主页界面；
    const handleOpenTypeModal = (modalType) => {
        if(modalType === 6){
            setNumberKeyboardVisible(true)
            return
        }
        setTypeModalVisible(true)
        setModalType(modalType)
        timer.current = setTimeout(()=>{
            if(nextpage){
                // 从主页进入
                history.push(nextpage);
            } else {
                // 查询用户待办信息    
                if(backlogList.length===0){
                    history.push("/recommenu")  //智能推荐科室
                } else {
                    history.push("/backlog")  //待办列表
                }
            }
        },3000)
    }

    const handleKeyConfirm  = (key) => {
        console.log(key)
        history.push(nextpage);
    }

    const handleCloseTypeModal = () => {
        setTypeModalVisible(false)
        timer.current && clearTimeout(timer.current)
    }
    
    return (
        <>
            <ModuleHeader history={history} title={""}/>
            <div className='identity-wrapper'>
                <h2 className='blue-text'>请选择登录方式</h2>
                <div className="card-wrapper social">
                    <div className='card-bg-title-wrapper'><span>医保登录</span></div>
                    <div className='card-content-wrapper'>
                        <Row gutter={[10, 5]} justify="space-around">
                            <Col span={8} onClick={()=>handleOpenTypeModal(3)}>
                                <CardBox title="医保刷脸" typeStr={"socialFace"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(2)}>
                                <CardBox title="医保码" typeStr={"elMedCard"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(1)}>
                                <CardBox title="社保卡" typeStr={"socialCard"}/>
                            </Col>
                        </Row>
                    </div>
                </div>
                <div className="card-wrapper selfpay">
                    <div className='card-bg-title-wrapper'><span>自费登录</span></div>
                    <div className='card-content-wrapper'>
                        <Row gutter={[10, 5]} justify="space-around">
                            <Col span={8} onClick={()=>handleOpenTypeModal(0)}>
                                <CardBox title="身份证" typeStr={"idCard"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(4)}>
                                <CardBox title="电子健康卡" typeStr={"elHealthCard"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(5)}>
                                <CardBox title="就诊卡" typeStr={"jzkCard"}/>
                            </Col>
                        </Row>
                        <Row gutter={[10, 5]} justify="space-around" style={{marginTop: 10}}>
                            <Col span={8} onClick={()=>handleOpenFaceModal()}>
                                <CardBox title="自费刷脸" typeStr={"face"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(8)}>
                                <CardBox title="扫描门诊号" typeStr={"scanCode"}/>
                            </Col>
                            <Col span={8} onClick={()=>handleOpenTypeModal(7)}>
                                <CardBox title="外国人永居证" typeStr={"foreignerCard"}/>
                            </Col>
                        </Row>
                    </div>
                </div>
                { typeModalVisible && <TypeModal 
                    modalVisible={typeModalVisible}
                    modalType={modalType}
                    onCancel={handleCloseTypeModal}
                />}
                { faceModalVisible && <BrushFaceModal 
                    modalVisible={faceModalVisible}
                    onCancel={handleCloseFaceModal}
                />}
                { numberKeyboardVisible && <NumKeyboardDrawer 
                    title={"就诊卡号"} 
                    visible={numberKeyboardVisible}
                    onCancel={handleCloseKeyModal}
                    onConfirm={handleKeyConfirm}
                />}
            </div>
        </>
    )
    
}

export default connect(({ user }) => ({
    user
}))(Identity);