/*
 * @Description:
    报告打印弹窗
 */
import React, { useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const PrintModal = ({
    modalVisible,
    title,
    subTitle,
    onCancel,
}) => {

    return (
        <Modal 
            destroyOnClose
            title={null}
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={true}
            closable={false}
            width={846}
            centered
            className="public-modal-wrapper print-modal"
            bodyStyle={{
                minHeight: 648,
            }}
            getContainer={document.getElementById("_module")}
        >
            <ModalCloseItem onCancel={onCancel} num={window.config.PRINT_OVER_TIME}/>
            <div className='modal-wrapper'>
                <img className="modal-img" src={require("../../assets/images/modal/confirm.png")} alt="提示" />
                <h2 className='modal-text'>{title}</h2>
            </div>
            {
                subTitle && 
                <h2 className='sub-text'>{subTitle}</h2>
            }
            <div className='modal-img-box'>
                <img src={require("../../assets/images/modal/report-print-modal-tips.png")} alt="" />
            </div>
        </Modal>
    )

}

export default PrintModal;