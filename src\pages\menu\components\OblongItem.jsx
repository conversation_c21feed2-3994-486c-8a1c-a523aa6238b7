
/*
 * @Description: 长方形样式按钮
 */
import React from 'react';
import { Link } from 'dva/router';
import Config from '../../../config';

const OblongItem = ({
    data,
    index,
    shoNoinline,
    findUrl
}) => {

    return (
        shoNoinline ?
            <div className={`oblong-button-wrapper bg${index}`} >
                <div className='top'>
                    <div className="icon-box-2">
                        {Config.IMGS_TYPE[data.type] ?
                            <img className='menu-icon' src={require("../../../assets/images/icons/" + Config.IMGS_TYPE[data.type] + ".png")} alt="图标" /> :
                            <img className='menu-icon' src="" alt="图标" />
                        }
                    </div>
                    <p className='menu-text'>{data.name}</p>
                </div>
                <p className='menu-tips'>{Config.TIPS_TYPE[data.type]}</p>
            </div>:
            
            <Link to={findUrl(data)}>
                <div className={`oblong-button-wrapper bg${index}`}>
                    <div className='top'>
                        <div className="icon-box-2">
                            {Config.IMGS_TYPE[data.type] ?
                                <img className='menu-icon' src={require("../../../assets/images/icons/" + Config.IMGS_TYPE[data.type] + ".png")} alt="图标" /> :
                                <img className='menu-icon' src="" alt="图标" />
                            }
                        </div>
                        <p className='menu-text'>{data.name}</p>
                    </div>
                    <p className='menu-tips'>{Config.TIPS_TYPE[data.type]}</p>
                </div>
            </Link>

    )
}

export default OblongItem;