import React from 'react';
import { Row, Col } from 'antd';

const TicketJfListTable = ({
    list,
    allChecked,
    onCheck,
    onAllChecked
}) => {

    return (
        <>
            <div className='thead-row-box'>
                <Row>
                    <Col span={3}>
                        <div className='checked-box' onClick={onAllChecked}>
                            {
                                allChecked ? 
                                <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/checked.png")} />:
                                <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/no-check.png")} />
                            }
                        </div>
                    </Col>
                    <Col span={10}>
                        <div className='table-title-box title-left'>
                            <b className='title'>缴费时间</b>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className='table-title-box'>
                            <b className='title'>缴费金额</b>
                        </div>
                    </Col>
                    <Col span={5}>
                        <div className='table-title-box'>
                            <b className='title'>缴费类型</b>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className='tbody-row-box'>
                {
                    list.map((item, index) => {
                        return (
                            <Row key={index} >
                                <Col span={3}>
                                    <div className='checked-box' onClick={()=>onCheck(item)}>
                                        {
                                            item.checked ?
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/checked.png")} /> :
                                            <img className='item-checked' alt="选中图标" src={require("../../../assets/images/btns/no-check.png")} />
                                        }
                                    </div>
                                </Col>
                                <Col span={10}>
                                    <div className='table-title-box title-left'>
                                        <span className='title'>{item.billDate}</span>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.price}</span>
                                    </div>
                                </Col>
                                <Col span={5}>
                                    <div className='table-title-box'>
                                        <span className='title'>{item.type}</span>
                                    </div>
                                </Col>
                            </Row>
                        )
                    })
                }
            </div>
        </>
    )

}

export default TicketJfListTable;