// .confirm-page-wrapper {
//     width: 100%;
//     margin-top: 76px;
// }

// .confirm-info-list-wrapper {
//     position: relative;
//     width: 100%;
//     height: 736px;
//     padding: 88px 30px 0;
//     background-color: #F0F6FF;
//     border-radius: 22px;
//     border: 1px solid #99C4FF;
//     .red-text{
//         color: #FF6116;
//     }
//     .circle-box {
//         position: absolute;
//         top: 195px;
//         width: 28px;
//         height: 28px;
//         background-color: #ffffff;
//         border: 1px solid #99C4FF;
//         border-radius: 50%;
//         &.left {
//             left: -14px;
//             clip: rect(0 28px 28px 14px);  //图形裁剪
//         }

//         &.right {
//             right: -14px;
//             clip: rect(0 14px 28px 0);   //图形裁剪
//         }
//     }

//     .info-bold-box {
//         padding: 0 2px 10px;
//         border-bottom: 2px dashed #84B5FF;
//         margin-bottom: 23px;
//     }

//     .info-bold {
//         color: #333333;
//         font-size: 28px;
//         line-height: 40px;
//         font-weight: 600;
//         margin-bottom: 15px;
//     }

//     .item-message {
//         display: flex;
//         align-items: center;
//         justify-content: space-between;
//         padding: 0 2px;

//         >span {
//             font-size: 28px;
//             line-height: 41px;
//             color: #666666;
//             font-weight: 600;

//             &.black-span {
//                 color: #333333;
//             }
//         }
//     }
    
//     .info-button{
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         width: 280px;
//         height: 100px;
//         margin: 48px auto 0;
//         text-align: center;
//         color: #ffffff;
//         border-radius: 50px;
//         background-color: #1677FF;
//         font-size: 42px;
//     }

// }