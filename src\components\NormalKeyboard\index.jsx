
import React, { useState, useEffect, useRef } from 'react';
import { Drawer, Row, Col } from 'antd';
import InfoModal from '../InfoModal';
import ModalCloseItem from '../ModalCloseItem';
import './index.scss';

const btnArray = [
    [ 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    [ 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', '关闭'],
    [ 'z', 'x', 'c', 'v', 'b', 'n', 'm', '退格', '清除']
];

const NumKeyboardDrawer = ({
    visible,
    title,
    value,
    onCancel,
    onConfirm,
    length,
    type,
    onListenKeyInput
}) => {
    const [dataKey, setmyKeys] = useState(value);
    const refs = useRef([])
    
    const keyOpt = (val) => {
        if (dataKey.length === length) {
            return
        }
        if(val === '关闭'){
            onCancel()
        }else if(val === '退格'){
            const item = [...dataKey];
            item.splice(item.length - 1, 1);
            setmyKeys(item.join(''));
            onListenKeyInput(item.join(''))
        }else if(val === '清除') {
            setmyKeys('');
            onListenKeyInput('')
        }else{
            const item = [...dataKey, val].join('')
            setmyKeys(item);
            onListenKeyInput(item)
        }
    };

    useEffect(() => {
        setmyKeys(value)
    }, [value])

    const handleTouchStart = (e, i, key) => {
        refs.current[i].style.backgroundColor = "#CCCCCC80"
        refs.current[i].style.color = "#eee"
    }

    const handleTouchEnd = (e, i, key) => {
        refs.current[i].style.backgroundColor = "#CCCCCC"
        refs.current[i].style.color = "#333"
        keyOpt(key)
        e.stopPropagation()
    }

    return (
        <Drawer
            title={null}
            placement={"bottom"}
            closable={false}
            maskClosable={true}
            onClose={onCancel}
            visible={visible}
            key={"num-keyboard"}
            height={400}
            className="normal-keyboard-drawer"
            getContainer={document.getElementById("_module")}
        >
            <Row className="normal-keyboard-wrapper" gutter={[0, 20]} >
                {
                    btnArray.map((item, rIndex) => {
                        return (
                            <Col span={24} key={`${rIndex}`}>
                                <Row gutter={[0, 20]} className={`key-row${rIndex+1}`}>
                                    {
                                        item.map((i, cindex)=>{
                                            return <>
                                            {
                                                i === '关闭' || i === '退格' || i === '清除' ?
                                                <Col key={`${rIndex}-${cindex}`} className={"special-btn"} >
                                                    <div ref={el => (refs.current[`${rIndex}-${cindex}`] = el)} className='item-btn' onTouchStart={(e)=> handleTouchStart(e, `${rIndex}-${cindex}`, i)} onTouchEnd={(e)=>handleTouchEnd(e, `${rIndex}-${cindex}`, i)}>{i}</div>
                                                </Col> : 
                                                <Col key={`${rIndex}-${cindex}`} className={i === "" ? "right-none" : ""} >
                                                    <div ref={el => (refs.current[`${rIndex}-${cindex}`] = el)} className='item-btn' onTouchStart={(e)=> handleTouchStart(e, `${rIndex}-${cindex}`, i)} onTouchEnd={(e)=>handleTouchEnd(e, `${rIndex}-${cindex}`, i)}>{i.toUpperCase()}</div>
                                                </Col>
                                            } </>
                                        })
                                    }
                                </Row>
                            </Col>
                        )
                    })
                }
            </Row>
        </Drawer>
    )
}

export default NumKeyboardDrawer;